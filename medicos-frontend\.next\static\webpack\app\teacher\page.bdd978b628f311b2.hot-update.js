"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast, dismiss } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please enter a title for the question paper\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.questionType) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select an exam type\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.subject) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select a subject\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            const questionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"Question paper created:\", questionPaper);\n            // Show creation success\n            toast({\n                title: \"Question Paper Created\",\n                description: \"Starting PDF download...\",\n                variant: \"default\"\n            });\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Success!\",\n                description: \"Question paper generated and downloaded successfully!\",\n                variant: \"default\"\n            });\n            // Reset to first step and clear form data after a short delay\n            setTimeout(()=>{\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n            }, 1000) // 1 second delay to ensure toast is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            console.error(\"Error submitting form:\", error);\n            let errorMessage = \"An unknown error occurred\";\n            let errorTitle = \"Error\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === 'string') {\n                errorMessage = error;\n            }\n            // Handle specific error types with better titles and messages\n            if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                errorTitle = \"Authentication Error\";\n                errorMessage = \"Please log in again to continue. Your session may have expired.\";\n            } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                errorTitle = \"Network Error\";\n                errorMessage = \"Please check your internet connection and try again.\";\n            } else if (errorMessage.includes(\"unused questions available\")) {\n                errorTitle = \"Insufficient Questions\";\n                // Extract numbers from the error message for a clearer explanation\n                const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                if (match) {\n                    const available = match[1];\n                    const requested = match[2];\n                    errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                }\n            } else if (errorMessage.includes(\"questions available\")) {\n                errorTitle = \"Question Availability Issue\";\n            // Keep the original message\n            }\n            // Always show toast with proper error handling\n            console.log(\"Showing error toast:\", {\n                errorTitle,\n                errorMessage\n            });\n            // Force the toast to show by clearing existing toasts and using a unique ID\n            setTimeout(()=>{\n                console.log(\"Attempting to show toast now...\");\n                // Clear any existing toasts first\n                dismiss();\n                // Wait a bit more then show the error toast\n                setTimeout(()=>{\n                    console.log(\"Showing error toast after clearing...\");\n                    // Try the hook-based toast with unique ID\n                    const toastId = \"error-\".concat(Date.now());\n                    toast({\n                        id: toastId,\n                        title: errorTitle,\n                        description: errorMessage,\n                        variant: \"destructive\"\n                    });\n                    // Also try the standalone toast as backup\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                        id: \"standalone-\".concat(Date.now()),\n                        title: errorTitle,\n                        description: errorMessage,\n                        variant: \"destructive\"\n                    });\n                }, 200);\n            }, 300); // Initial delay to ensure any loading states are cleared\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_10__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 380,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    // Test toast function\n    const testToast = ()=>{\n        console.log(\"Test toast clicked\");\n        toast({\n            title: \"Test Toast (Hook)\",\n            description: \"This is a test toast using the hook method\",\n            variant: \"destructive\"\n        });\n        // Also test standalone\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n            title: \"Test Toast (Standalone)\",\n            description: \"This is a test toast using the standalone method\",\n            variant: \"destructive\"\n        });\n    };\n    // Test error toast function\n    const testErrorToast = ()=>{\n        console.log(\"Test error toast clicked\");\n        const errorTitle = \"Insufficient Questions\";\n        const errorMessage = \"Only 2 questions are available for this subject/topic, but you requested 3 questions. Please reduce the number of questions or add more questions to the database.\";\n        setTimeout(()=>{\n            console.log(\"Showing test error toast...\");\n            toast({\n                title: errorTitle,\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: errorTitle,\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n        }, 500);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: testToast,\n                className: \"px-4 py-2 bg-red-500 text-white rounded\",\n                style: {\n                    position: 'fixed',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 9999\n                },\n                children: \"Test Toast\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: testErrorToast,\n                className: \"px-4 py-2 bg-orange-500 text-white rounded\",\n                style: {\n                    position: 'fixed',\n                    top: '60px',\n                    right: '10px',\n                    zIndex: 9999\n                },\n                children: \"Test Error Toast\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 424,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"7Q8YNZ7kPiKZDAQZbtUWLpy+yRM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3RlYWNoZXIvcXVlc3Rpb24tcGFwZXItd2l6YXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUM2RTtBQUMvQjtBQUNqQjtBQUNFO0FBQ0k7QUFDSTtBQUNFO0FBQ1I7QUFDZjtBQUNDO0FBQ3NDO0FBOEJ6RixNQUFNZSxrQkFBNEI7SUFDaENDLGNBQWM7SUFDZEMsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFFBQVE7SUFDUkMsU0FBUztJQUNUQyxnQkFBZ0I7SUFDaEJDLGtCQUFrQjtRQUNoQkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLE1BQU07SUFDUjtJQUNBQyxtQkFBbUI7SUFDbkJDLFlBQVk7SUFDWkMsZ0JBQWdCO0lBQ2hCQyxVQUFVO0lBQ1ZDLGNBQWM7SUFDZEMsU0FBU0M7QUFDWDtBQUVPLFNBQVNDOztJQUNkLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHbkMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDb0MsVUFBVUMsWUFBWSxHQUFHckMsK0NBQVFBLENBQVdlO0lBQ25ELE1BQU0sQ0FBQ3VCLGNBQWNDLGdCQUFnQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxFQUFFSSxLQUFLLEVBQUVvQyxPQUFPLEVBQUUsR0FBR3JDLGtFQUFRQTtJQUVuQyxNQUFNc0MsaUJBQWlCLENBQUNDO1FBQ3RCTCxZQUFZLENBQUNNLE9BQVU7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRSxHQUFHRCxJQUFJO1lBQUM7SUFDNUM7SUFFQSxNQUFNRSxXQUFXO1FBQ2ZULGVBQWUsQ0FBQ1EsT0FBU0UsS0FBS0MsR0FBRyxDQUFDSCxPQUFPLEdBQUdJLE1BQU1DLE1BQU0sR0FBRztJQUM3RDtJQUVBLE1BQU1DLFdBQVc7UUFDZmQsZUFBZSxDQUFDUSxPQUFTRSxLQUFLSyxHQUFHLENBQUNQLE9BQU8sR0FBRztJQUM5QztJQUVBLE1BQU1RLFdBQVc7UUFDZlA7SUFDRjtJQUVBLE1BQU1RLGVBQWU7UUFDbkIsSUFBSWQsY0FBYyxRQUFRLCtCQUErQjtRQUV6RCxJQUFJO2dCQWdCR0Y7WUFmTEcsZ0JBQWdCO1lBQ2hCYyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CbEI7WUFFaEMsZ0NBQWdDO1lBQ2hDLE1BQU1tQixlQUFlQyxhQUFhQyxPQUFPLENBQUM7WUFDMUMsTUFBTUMsZ0JBQWdCRixhQUFhQyxPQUFPLENBQUM7WUFDM0MsTUFBTUUsUUFBUUgsYUFBYUMsT0FBTyxDQUFDO1lBRW5DSixRQUFRQyxHQUFHLENBQUMscUJBQXFCO2dCQUMvQkMsY0FBY0EsZUFBZSxHQUFpQyxPQUE5QkEsYUFBYUssU0FBUyxDQUFDLEdBQUcsS0FBSSxTQUFPO2dCQUNyRUYsZUFBZUEsZ0JBQWdCLEdBQWtDLE9BQS9CQSxjQUFjRSxTQUFTLENBQUMsR0FBRyxLQUFJLFNBQU87Z0JBQ3hFRCxPQUFPQSxRQUFRLEdBQTBCLE9BQXZCQSxNQUFNQyxTQUFTLENBQUMsR0FBRyxLQUFJLFNBQU87WUFDbEQ7WUFFQSwyQkFBMkI7WUFDM0IsSUFBSSxHQUFDeEIsa0JBQUFBLFNBQVNuQixLQUFLLGNBQWRtQixzQ0FBQUEsZ0JBQWdCeUIsSUFBSSxLQUFJO2dCQUMzQnpELE1BQU07b0JBQ0phLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2I0QyxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7WUFFQSxJQUFJLENBQUMxQixTQUFTcEIsWUFBWSxFQUFFO2dCQUMxQlosTUFBTTtvQkFDSmEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYjRDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLElBQUksQ0FBQzFCLFNBQVNoQixPQUFPLEVBQUU7Z0JBQ3JCaEIsTUFBTTtvQkFDSmEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYjRDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLDBCQUEwQjtZQUMxQixNQUFNQyxhQUFxQztnQkFDekM5QyxPQUFPbUIsU0FBU25CLEtBQUs7Z0JBQ3JCQyxhQUFha0IsU0FBU2xCLFdBQVc7Z0JBQ2pDRSxTQUFTZ0IsU0FBU2hCLE9BQU87Z0JBQ3pCTyxZQUFZUyxTQUFTVCxVQUFVO2dCQUMvQkUsVUFBVU8sU0FBU1AsUUFBUTtnQkFDM0JtQyxVQUFVNUIsU0FBU3BCLFlBQVk7Z0JBQy9CYyxjQUFjTSxTQUFTTixZQUFZO2dCQUNuQ0MsU0FBU0ssU0FBU0wsT0FBTztnQkFDekJrQyxjQUFjN0IsU0FBU1YsaUJBQWlCO1lBQzFDO1lBRUEscUNBQXFDO1lBQ3JDLElBQUlVLFNBQVNmLGNBQWMsS0FBSyxVQUFVO2dCQUN4QzBDLFdBQVdHLFNBQVMsR0FBRztvQkFDckJDLGtCQUFrQjt3QkFDaEJDLGdCQUFnQmhDLFNBQVNkLGdCQUFnQixDQUFDQyxJQUFJO3dCQUM5QzhDLGtCQUFrQmpDLFNBQVNkLGdCQUFnQixDQUFDRSxNQUFNO3dCQUNsRDhDLGdCQUFnQmxDLFNBQVNkLGdCQUFnQixDQUFDRyxJQUFJO29CQUNoRDtvQkFDQUMsbUJBQW1CVSxTQUFTVixpQkFBaUI7b0JBQzdDQyxZQUFZUyxTQUFTVCxVQUFVO29CQUMvQkUsVUFBVU8sU0FBU1AsUUFBUTtvQkFDM0JELGdCQUFnQlEsU0FBU1IsY0FBYztnQkFDekM7WUFDRixPQUFPO2dCQUNMLGlFQUFpRTtnQkFDakVtQyxXQUFXRyxTQUFTLEdBQUc7b0JBQ3JCQyxrQkFBa0I7d0JBQ2hCQyxnQkFBZ0I7d0JBQ2hCQyxrQkFBa0I7d0JBQ2xCQyxnQkFBZ0I7b0JBQ2xCO29CQUNBNUMsbUJBQW1CVSxTQUFTVixpQkFBaUI7b0JBQzdDQyxZQUFZUyxTQUFTVCxVQUFVO29CQUMvQkUsVUFBVU8sU0FBU1AsUUFBUTtvQkFDM0JELGdCQUFnQlEsU0FBU1IsY0FBYztnQkFDekM7WUFDRjtZQUVBLDRCQUE0QjtZQUM1QixNQUFNMkMsZ0JBQWdCLE1BQU10RSw0RUFBbUJBLENBQUM4RDtZQUNoRFYsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQmlCO1lBRXZDLHdCQUF3QjtZQUN4Qm5FLE1BQU07Z0JBQ0phLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2I0QyxTQUFTO1lBQ1g7WUFFQSxtQkFBbUI7WUFDbkIsTUFBTVUsVUFBVSxNQUFNdEUsOEVBQXFCQSxDQUFDcUUsY0FBY0UsR0FBRyxFQUFFO1lBRS9ELHVCQUF1QjtZQUN2QixNQUFNQyxNQUFNQyxPQUFPQyxHQUFHLENBQUNDLGVBQWUsQ0FBQ0w7WUFDdkMsTUFBTU0sT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUdQO1lBQ1pJLEtBQUtJLFFBQVEsR0FBRyxHQUEwQ0MsT0FBdkMvQyxTQUFTbkIsS0FBSyxDQUFDbUUsT0FBTyxDQUFDLFFBQVEsTUFBSyxLQUFjLE9BQVhELEtBQUtFLEdBQUcsSUFBRztZQUNyRU4sU0FBU08sSUFBSSxDQUFDQyxXQUFXLENBQUNUO1lBQzFCQSxLQUFLVSxLQUFLO1lBQ1ZULFNBQVNPLElBQUksQ0FBQ0csV0FBVyxDQUFDWDtZQUMxQkgsT0FBT0MsR0FBRyxDQUFDYyxlQUFlLENBQUNoQjtZQUUzQnRFLE1BQU07Z0JBQ0phLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2I0QyxTQUFTO1lBQ1g7WUFFQSw4REFBOEQ7WUFDOUQ2QixXQUFXO2dCQUNUeEQsZUFBZTtnQkFDZkUsWUFBWXRCO2dCQUNad0IsZ0JBQWdCO1lBQ2xCLEdBQUcsTUFBTSw0Q0FBNEM7O1FBQ3ZELEVBQUUsT0FBT3FELE9BQU87WUFDZHJELGdCQUFnQjtZQUNoQmMsUUFBUXVDLEtBQUssQ0FBQywwQkFBMEJBO1lBRXhDLElBQUlDLGVBQWU7WUFDbkIsSUFBSUMsYUFBYTtZQUVqQixJQUFJRixpQkFBaUJHLE9BQU87Z0JBQzFCRixlQUFlRCxNQUFNSSxPQUFPO1lBQzlCLE9BQU8sSUFBSSxPQUFPSixVQUFVLFVBQVU7Z0JBQ3BDQyxlQUFlRDtZQUNqQjtZQUVBLDhEQUE4RDtZQUM5RCxJQUFJQyxhQUFhSSxRQUFRLENBQUMsOEJBQThCSixhQUFhSSxRQUFRLENBQUMsaUJBQWlCO2dCQUM3RkgsYUFBYTtnQkFDYkQsZUFBZTtZQUNqQixPQUFPLElBQUlBLGFBQWFJLFFBQVEsQ0FBQyxjQUFjSixhQUFhSSxRQUFRLENBQUMsVUFBVTtnQkFDN0VILGFBQWE7Z0JBQ2JELGVBQWU7WUFDakIsT0FBTyxJQUFJQSxhQUFhSSxRQUFRLENBQUMsK0JBQStCO2dCQUM5REgsYUFBYTtnQkFDYixtRUFBbUU7Z0JBQ25FLE1BQU1JLFFBQVFMLGFBQWFLLEtBQUssQ0FBQztnQkFDakMsSUFBSUEsT0FBTztvQkFDVCxNQUFNQyxZQUFZRCxLQUFLLENBQUMsRUFBRTtvQkFDMUIsTUFBTUUsWUFBWUYsS0FBSyxDQUFDLEVBQUU7b0JBQzFCTCxlQUFlLFFBQXVGTyxPQUEvRUQsV0FBVSx1RUFBK0UsT0FBVkMsV0FBVTtnQkFDbEg7WUFDRixPQUFPLElBQUlQLGFBQWFJLFFBQVEsQ0FBQyx3QkFBd0I7Z0JBQ3ZESCxhQUFhO1lBQ2IsNEJBQTRCO1lBQzlCO1lBRUEsK0NBQStDO1lBQy9DekMsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QjtnQkFBRXdDO2dCQUFZRDtZQUFhO1lBRS9ELDRFQUE0RTtZQUM1RUYsV0FBVztnQkFDVHRDLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixrQ0FBa0M7Z0JBQ2xDZDtnQkFFQSw0Q0FBNEM7Z0JBQzVDbUQsV0FBVztvQkFDVHRDLFFBQVFDLEdBQUcsQ0FBQztvQkFFWiwwQ0FBMEM7b0JBQzFDLE1BQU0rQyxVQUFVLFNBQW9CLE9BQVhsQixLQUFLRSxHQUFHO29CQUNqQ2pGLE1BQU07d0JBQ0prRyxJQUFJRDt3QkFDSnBGLE9BQU82RTt3QkFDUDVFLGFBQWEyRTt3QkFDYi9CLFNBQVM7b0JBQ1g7b0JBRUEsMENBQTBDO29CQUMxQ3pELCtEQUFlQSxDQUFDO3dCQUNkaUcsSUFBSSxjQUF5QixPQUFYbkIsS0FBS0UsR0FBRzt3QkFDMUJwRSxPQUFPNkU7d0JBQ1A1RSxhQUFhMkU7d0JBQ2IvQixTQUFTO29CQUNYO2dCQUNGLEdBQUc7WUFDTCxHQUFHLE1BQU0seURBQXlEO1FBQ3BFO0lBQ0Y7SUFFQSxNQUFNZixRQUFRO1FBQ1o7WUFDRTlCLE9BQU87WUFDUHNGLE1BQU07WUFDTkMseUJBQ0UsOERBQUNsRyx1RUFBZ0JBO2dCQUNmOEIsVUFBVUE7Z0JBQ1ZLLGdCQUFnQkE7Z0JBQ2hCZ0UsUUFBUTdEO2dCQUNSOEQsUUFBUXZEO2dCQUNSd0QsUUFBUTFEO2dCQUNSMkQsY0FBYzFFLGdCQUFnQjs7Ozs7O1FBR3BDO1FBQ0k7WUFDRmpCLE9BQU87WUFDUHNGLE1BQU07WUFDTkMseUJBQ0UsOERBQUMxRixvR0FBK0JBO2dCQUM5QnNCLFVBQVVBO2dCQUNWSyxnQkFBZ0JBO2dCQUNoQmdFLFFBQVE3RDtnQkFDUjhELFFBQVF2RDtnQkFDUndELFFBQVExRDtnQkFDUjJELGNBQWMxRSxnQkFBZ0I7Ozs7OztRQUdwQztRQUNBO1lBQ0VqQixPQUFPO1lBQ1BzRixNQUFNO1lBQ05DLHlCQUNFLDhEQUFDakcseUVBQWlCQTtnQkFDaEI2QixVQUFVQTtnQkFDVkssZ0JBQWdCQTtnQkFDaEJnRSxRQUFRN0Q7Z0JBQ1I4RCxRQUFRdkQ7Z0JBQ1J3RCxRQUFRMUQ7Z0JBQ1IyRCxjQUFjMUUsZ0JBQWdCOzs7Ozs7UUFHcEM7UUFDQTtZQUNFakIsT0FBTztZQUNQc0YsTUFBTTtZQUNOQyx5QkFDRSw4REFBQ2hHLDZFQUFtQkE7Z0JBQ2xCNEIsVUFBVUE7Z0JBQ1ZLLGdCQUFnQkE7Z0JBQ2hCZ0UsUUFBUTdEO2dCQUNSOEQsUUFBUXZEO2dCQUNSd0QsUUFBUTFEO2dCQUNSMkQsY0FBYzFFLGdCQUFnQjs7Ozs7O1FBR3BDO1FBQ0E7WUFDRWpCLE9BQU87WUFDUHNGLE1BQU07WUFDTkMseUJBQ0UsOERBQUMvRixpRkFBcUJBO2dCQUNwQjJCLFVBQVVBO2dCQUNWSyxnQkFBZ0JBO2dCQUNoQmdFLFFBQVE3RDtnQkFDUjhELFFBQVF2RDtnQkFDUndELFFBQVExRDtnQkFDUjJELGNBQWMxRSxnQkFBZ0I7Ozs7OztRQUdwQztRQUNBO1lBQ0VqQixPQUFPO1lBQ1BzRixNQUFNO1lBQ05DLHlCQUNFLDhEQUFDOUYsbUZBQXNCQTtnQkFDckIwQixVQUFVQTtnQkFDVkssZ0JBQWdCQTtnQkFDaEJnRSxRQUFRN0Q7Z0JBQ1I4RCxRQUFRdkQ7Z0JBQ1J3RCxRQUFRMUQ7Z0JBQ1IyRCxjQUFjMUUsZ0JBQWdCOzs7Ozs7UUFHcEM7UUFDQTtZQUNFakIsT0FBTztZQUNQc0YsTUFBTTtZQUNOQyx5QkFDRSw4REFBQzdGLDJFQUFrQkE7Z0JBQ2pCeUIsVUFBVUE7Z0JBQ1ZLLGdCQUFnQkE7Z0JBQ2hCZ0UsUUFBUTdEO2dCQUNSOEQsUUFBUXZEO2dCQUNSd0QsUUFBUTFEO2dCQUNSMkQsY0FBYzFFLGdCQUFnQjs7Ozs7O1FBR3BDO1FBQ0E7WUFDRWpCLE9BQU87WUFDUHNGLE1BQU07WUFDTkMseUJBQVcsOERBQUM1Riw2REFBV0E7Z0JBQUN3QixVQUFVQTtnQkFBVXlFLFVBQVV6RDtnQkFBYzBELFdBQVd4RTs7Ozs7O1FBQ2pGO0tBQ0Q7SUFFRCxzQkFBc0I7SUFDdEIsTUFBTXlFLFlBQVk7UUFDaEIxRCxRQUFRQyxHQUFHLENBQUM7UUFDWmxELE1BQU07WUFDSmEsT0FBTztZQUNQQyxhQUFhO1lBQ2I0QyxTQUFTO1FBQ1g7UUFFQSx1QkFBdUI7UUFDdkJ6RCwrREFBZUEsQ0FBQztZQUNkWSxPQUFPO1lBQ1BDLGFBQWE7WUFDYjRDLFNBQVM7UUFDWDtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU1rRCxpQkFBaUI7UUFDckIzRCxRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNd0MsYUFBYTtRQUNuQixNQUFNRCxlQUFlO1FBRXJCRixXQUFXO1lBQ1R0QyxRQUFRQyxHQUFHLENBQUM7WUFDWmxELE1BQU07Z0JBQ0phLE9BQU82RTtnQkFDUDVFLGFBQWEyRTtnQkFDYi9CLFNBQVM7WUFDWDtZQUVBekQsK0RBQWVBLENBQUM7Z0JBQ2RZLE9BQU82RTtnQkFDUDVFLGFBQWEyRTtnQkFDYi9CLFNBQVM7WUFDWDtRQUNGLEdBQUc7SUFDTDtJQUVBLHFCQUNFLDhEQUFDbUQ7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUNDQyxTQUFTTDtnQkFDVEcsV0FBVTtnQkFDVkcsT0FBTztvQkFBRUMsVUFBVTtvQkFBU0MsS0FBSztvQkFBUUMsT0FBTztvQkFBUUMsUUFBUTtnQkFBSzswQkFDdEU7Ozs7OzswQkFHRCw4REFBQ047Z0JBQ0NDLFNBQVNKO2dCQUNURSxXQUFVO2dCQUNWRyxPQUFPO29CQUFFQyxVQUFVO29CQUFTQyxLQUFLO29CQUFRQyxPQUFPO29CQUFRQyxRQUFRO2dCQUFLOzBCQUN0RTs7Ozs7OzBCQUlELDhEQUFDNUcsOERBQWFBO2dCQUFDcUIsYUFBYUE7Z0JBQWFhLE9BQU9BLE1BQU0yRSxHQUFHLENBQUMsQ0FBQ0MsT0FBVTt3QkFBRTFHLE9BQU8wRyxLQUFLMUcsS0FBSzt3QkFBRXNGLE1BQU1vQixLQUFLcEIsSUFBSTtvQkFBQzs7Ozs7O1lBQ3pHeEQsS0FBSyxDQUFDYixZQUFZLENBQUNzRSxTQUFTOzs7Ozs7O0FBR25DO0dBN1hnQnZFOztRQUlhOUIsOERBQVFBOzs7S0FKckI4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdGVhY2hlclxccXVlc3Rpb24tcGFwZXItd2l6YXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBjcmVhdGVRdWVzdGlvblBhcGVyLCBkb3dubG9hZFF1ZXN0aW9uUGFwZXIsIENyZWF0ZVF1ZXN0aW9uUGFwZXJEdG8gfSBmcm9tIFwiQC9saWIvYXBpL3F1ZXN0aW9uUGFwZXJzXCJcclxuaW1wb3J0IHsgdXNlVG9hc3QsIHRvYXN0IGFzIHN0YW5kYWxvbmVUb2FzdCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdXNlLXRvYXN0XCJcclxuaW1wb3J0IHsgUXVlc3Rpb25UeXBlU3RlcCB9IGZyb20gXCIuL3N0ZXBzL3F1ZXN0aW9uLXR5cGUtc3RlcFwiXHJcbmltcG9ydCB7IENvdXJzZVN1YmplY3RTdGVwIH0gZnJvbSBcIi4vc3RlcHMvY291cnNlLXN1YmplY3Qtc3RlcFwiXHJcbmltcG9ydCB7IERpZmZpY3VsdHlMZXZlbFN0ZXAgfSBmcm9tIFwiLi9zdGVwcy9kaWZmaWN1bHR5LWxldmVsLXN0ZXBcIlxyXG5pbXBvcnQgeyBRdWVzdGlvblNlbGVjdGlvblN0ZXAgfSBmcm9tIFwiLi9zdGVwcy9xdWVzdGlvbi1zZWxlY3Rpb24tc3RlcFwiXHJcbmltcG9ydCB7IFBhcGVyQ3VzdG9taXphdGlvblN0ZXAgfSBmcm9tIFwiLi9zdGVwcy9wYXBlci1jdXN0b21pemF0aW9uLXN0ZXBcIlxyXG5pbXBvcnQgeyBJbmNsdWRlQW5zd2Vyc1N0ZXAgfSBmcm9tIFwiLi9zdGVwcy9pbmNsdWRlLWFuc3dlcnMtc3RlcFwiXHJcbmltcG9ydCB7IEFjdGlvbnNTdGVwIH0gZnJvbSBcIi4vc3RlcHMvYWN0aW9ucy1zdGVwXCJcclxuaW1wb3J0IHsgU3RlcEluZGljYXRvciB9IGZyb20gXCIuL3VpL3N0ZXAtaW5kaWNhdG9yXCJcclxuaW1wb3J0IHsgUXVlc3Rpb25UaXRsZUFuZERlc2NyaXB0aW9uU3RlcCB9IGZyb20gXCIuL3N0ZXBzL3F1ZXN0aW9uLXRpdGxlLWRlc2NyaXB0aW9uLXN0ZXBcIlxyXG5cclxuZXhwb3J0IHR5cGUgRm9ybURhdGEgPSB7XHJcbiAgLy8gUXVlc3Rpb25UeXBlU3RlcCAtIG1hcHMgdG8gZXhhbVR5cGVcclxuICBxdWVzdGlvblR5cGU6IHN0cmluZ1xyXG4gIC8vIFF1ZXN0aW9uVGl0bGVBbmREZXNjcmlwdGlvblN0ZXAgLSBtYXBzIHRvIHRpdGxlLCBkZXNjcmlwdGlvblxyXG4gIHRpdGxlOiBzdHJpbmdcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nXHJcbiAgLy8gQ291cnNlU3ViamVjdFN0ZXAgLSBtYXBzIHRvIHN1YmplY3RcclxuICBjb3Vyc2U6IHN0cmluZ1xyXG4gIHN1YmplY3Q6IHN0cmluZ1xyXG4gIC8vIERpZmZpY3VsdHlMZXZlbFN0ZXAgLSBtYXBzIHRvIGN1c3RvbWlzZS5jdXN0b21EaWZmaWN1bHR5IG9yIGF1dG9cclxuICBkaWZmaWN1bHR5TW9kZTogXCJhdXRvXCIgfCBcImN1c3RvbVwiXHJcbiAgZGlmZmljdWx0eUxldmVsczoge1xyXG4gICAgZWFzeTogbnVtYmVyXHJcbiAgICBtZWRpdW06IG51bWJlclxyXG4gICAgaGFyZDogbnVtYmVyXHJcbiAgfVxyXG4gIC8vIFF1ZXN0aW9uU2VsZWN0aW9uU3RlcCAtIG1hcHMgdG8gbWF4UXVlc3Rpb25zIGFuZCBjdXN0b21pc2UubnVtYmVyT2ZRdWVzdGlvbnNcclxuICBudW1iZXJPZlF1ZXN0aW9uczogbnVtYmVyXHJcbiAgLy8gUGFwZXJDdXN0b21pemF0aW9uU3RlcCAtIG1hcHMgdG8gdG90YWxNYXJrcyBhbmQgY3VzdG9taXNlLnRvdGFsTWFya3NcclxuICB0b3RhbE1hcmtzOiBudW1iZXJcclxuICAvLyBJbmNsdWRlQW5zd2Vyc1N0ZXAgLSBtYXBzIHRvIGN1c3RvbWlzZS5pbmNsdWRlQW5zd2Vyc1xyXG4gIGluY2x1ZGVBbnN3ZXJzOiBib29sZWFuXHJcbiAgLy8gQWRkaXRpb25hbCBmaWVsZHMgZm9yIEFQSVxyXG4gIGR1cmF0aW9uOiBudW1iZXIgLy8gRGVmYXVsdCA2MCBtaW51dGVzICgxIGhvdXIpXHJcbiAgaW5zdHJ1Y3Rpb25zOiBzdHJpbmdcclxuICB0b3BpY0lkPzogc3RyaW5nXHJcbn1cclxuXHJcbmNvbnN0IGluaXRpYWxGb3JtRGF0YTogRm9ybURhdGEgPSB7XHJcbiAgcXVlc3Rpb25UeXBlOiBcIlwiLFxyXG4gIHRpdGxlOiBcIlwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIlwiLFxyXG4gIGNvdXJzZTogXCJcIixcclxuICBzdWJqZWN0OiBcIlwiLFxyXG4gIGRpZmZpY3VsdHlNb2RlOiBcImF1dG9cIixcclxuICBkaWZmaWN1bHR5TGV2ZWxzOiB7XHJcbiAgICBlYXN5OiAzMCxcclxuICAgIG1lZGl1bTogNTAsXHJcbiAgICBoYXJkOiAyMCxcclxuICB9LFxyXG4gIG51bWJlck9mUXVlc3Rpb25zOiAxLFxyXG4gIHRvdGFsTWFya3M6IDEwMCxcclxuICBpbmNsdWRlQW5zd2VyczogZmFsc2UsIC8vIERlZmF1bHQgdG8gZmFsc2UgYXMgcGVyIHJlcXVpcmVtZW50c1xyXG4gIGR1cmF0aW9uOiA2MCwgLy8gRGVmYXVsdCAxIGhvdXIgaW4gbWludXRlc1xyXG4gIGluc3RydWN0aW9uczogXCJcIixcclxuICB0b3BpY0lkOiB1bmRlZmluZWQsXHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBRdWVzdGlvblBhcGVyV2l6YXJkKCkge1xyXG4gIGNvbnN0IFtjdXJyZW50U3RlcCwgc2V0Q3VycmVudFN0ZXBdID0gdXNlU3RhdGUoMClcclxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPEZvcm1EYXRhPihpbml0aWFsRm9ybURhdGEpXHJcbiAgY29uc3QgW2lzR2VuZXJhdGluZywgc2V0SXNHZW5lcmF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IHsgdG9hc3QsIGRpc21pc3MgfSA9IHVzZVRvYXN0KClcclxuXHJcbiAgY29uc3QgdXBkYXRlRm9ybURhdGEgPSAoZGF0YTogUGFydGlhbDxGb3JtRGF0YT4pID0+IHtcclxuICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoeyAuLi5wcmV2LCAuLi5kYXRhIH0pKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgbmV4dFN0ZXAgPSAoKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50U3RlcCgocHJldikgPT4gTWF0aC5taW4ocHJldiArIDEsIHN0ZXBzLmxlbmd0aCAtIDEpKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgcHJldlN0ZXAgPSAoKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50U3RlcCgocHJldikgPT4gTWF0aC5tYXgocHJldiAtIDEsIDApKVxyXG4gIH1cclxuXHJcbiAgY29uc3Qgc2tpcFN0ZXAgPSAoKSA9PiB7XHJcbiAgICBuZXh0U3RlcCgpXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoaXNHZW5lcmF0aW5nKSByZXR1cm47IC8vIFByZXZlbnQgbXVsdGlwbGUgc3VibWlzc2lvbnNcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0dlbmVyYXRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiU3VibWl0dGluZyBkYXRhOlwiLCBmb3JtRGF0YSlcclxuXHJcbiAgICAgIC8vIERlYnVnOiBDaGVjayBhdmFpbGFibGUgdG9rZW5zXHJcbiAgICAgIGNvbnN0IGJhY2tlbmRUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xyXG4gICAgICBjb25zdCBmaXJlYmFzZVRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJmaXJlYmFzZVRva2VuXCIpO1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidG9rZW5cIik7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkF2YWlsYWJsZSB0b2tlbnM6XCIsIHtcclxuICAgICAgICBiYWNrZW5kVG9rZW46IGJhY2tlbmRUb2tlbiA/IGAke2JhY2tlbmRUb2tlbi5zdWJzdHJpbmcoMCwgMjApfS4uLmAgOiAnTm9uZScsXHJcbiAgICAgICAgZmlyZWJhc2VUb2tlbjogZmlyZWJhc2VUb2tlbiA/IGAke2ZpcmViYXNlVG9rZW4uc3Vic3RyaW5nKDAsIDIwKX0uLi5gIDogJ05vbmUnLFxyXG4gICAgICAgIHRva2VuOiB0b2tlbiA/IGAke3Rva2VuLnN1YnN0cmluZygwLCAyMCl9Li4uYCA6ICdOb25lJ1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xyXG4gICAgICBpZiAoIWZvcm1EYXRhLnRpdGxlPy50cmltKCkpIHtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogXCJWYWxpZGF0aW9uIEVycm9yXCIsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJQbGVhc2UgZW50ZXIgYSB0aXRsZSBmb3IgdGhlIHF1ZXN0aW9uIHBhcGVyXCIsXHJcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoIWZvcm1EYXRhLnF1ZXN0aW9uVHlwZSkge1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiBcIlZhbGlkYXRpb24gRXJyb3JcIixcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIlBsZWFzZSBzZWxlY3QgYW4gZXhhbSB0eXBlXCIsXHJcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoIWZvcm1EYXRhLnN1YmplY3QpIHtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogXCJWYWxpZGF0aW9uIEVycm9yXCIsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJQbGVhc2Ugc2VsZWN0IGEgc3ViamVjdFwiLFxyXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gUHJlcGFyZSB0aGUgQVBJIHBheWxvYWRcclxuICAgICAgY29uc3QgYXBpUGF5bG9hZDogQ3JlYXRlUXVlc3Rpb25QYXBlckR0byA9IHtcclxuICAgICAgICB0aXRsZTogZm9ybURhdGEudGl0bGUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGZvcm1EYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgIHN1YmplY3Q6IGZvcm1EYXRhLnN1YmplY3QsXHJcbiAgICAgICAgdG90YWxNYXJrczogZm9ybURhdGEudG90YWxNYXJrcyxcclxuICAgICAgICBkdXJhdGlvbjogZm9ybURhdGEuZHVyYXRpb24sXHJcbiAgICAgICAgZXhhbVR5cGU6IGZvcm1EYXRhLnF1ZXN0aW9uVHlwZSxcclxuICAgICAgICBpbnN0cnVjdGlvbnM6IGZvcm1EYXRhLmluc3RydWN0aW9ucyxcclxuICAgICAgICB0b3BpY0lkOiBmb3JtRGF0YS50b3BpY0lkLFxyXG4gICAgICAgIG1heFF1ZXN0aW9uczogZm9ybURhdGEubnVtYmVyT2ZRdWVzdGlvbnMsXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEFkZCBjdXN0b21pemF0aW9uIGlmIG5vdCBhdXRvIG1vZGVcclxuICAgICAgaWYgKGZvcm1EYXRhLmRpZmZpY3VsdHlNb2RlID09PSBcImN1c3RvbVwiKSB7XHJcbiAgICAgICAgYXBpUGF5bG9hZC5jdXN0b21pc2UgPSB7XHJcbiAgICAgICAgICBjdXN0b21EaWZmaWN1bHR5OiB7XHJcbiAgICAgICAgICAgIGVhc3lQZXJjZW50YWdlOiBmb3JtRGF0YS5kaWZmaWN1bHR5TGV2ZWxzLmVhc3ksXHJcbiAgICAgICAgICAgIG1lZGl1bVBlcmNlbnRhZ2U6IGZvcm1EYXRhLmRpZmZpY3VsdHlMZXZlbHMubWVkaXVtLFxyXG4gICAgICAgICAgICBoYXJkUGVyY2VudGFnZTogZm9ybURhdGEuZGlmZmljdWx0eUxldmVscy5oYXJkLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIG51bWJlck9mUXVlc3Rpb25zOiBmb3JtRGF0YS5udW1iZXJPZlF1ZXN0aW9ucyxcclxuICAgICAgICAgIHRvdGFsTWFya3M6IGZvcm1EYXRhLnRvdGFsTWFya3MsXHJcbiAgICAgICAgICBkdXJhdGlvbjogZm9ybURhdGEuZHVyYXRpb24sXHJcbiAgICAgICAgICBpbmNsdWRlQW5zd2VyczogZm9ybURhdGEuaW5jbHVkZUFuc3dlcnMsXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIEZvciBhdXRvIG1vZGUsIHN0aWxsIGluY2x1ZGUgY3VzdG9taXphdGlvbiB3aXRoIGRlZmF1bHQgdmFsdWVzXHJcbiAgICAgICAgYXBpUGF5bG9hZC5jdXN0b21pc2UgPSB7XHJcbiAgICAgICAgICBjdXN0b21EaWZmaWN1bHR5OiB7XHJcbiAgICAgICAgICAgIGVhc3lQZXJjZW50YWdlOiAzMCxcclxuICAgICAgICAgICAgbWVkaXVtUGVyY2VudGFnZTogNTAsXHJcbiAgICAgICAgICAgIGhhcmRQZXJjZW50YWdlOiAyMCxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBudW1iZXJPZlF1ZXN0aW9uczogZm9ybURhdGEubnVtYmVyT2ZRdWVzdGlvbnMsXHJcbiAgICAgICAgICB0b3RhbE1hcmtzOiBmb3JtRGF0YS50b3RhbE1hcmtzLFxyXG4gICAgICAgICAgZHVyYXRpb246IGZvcm1EYXRhLmR1cmF0aW9uLFxyXG4gICAgICAgICAgaW5jbHVkZUFuc3dlcnM6IGZvcm1EYXRhLmluY2x1ZGVBbnN3ZXJzLFxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ3JlYXRlIHRoZSBxdWVzdGlvbiBwYXBlclxyXG4gICAgICBjb25zdCBxdWVzdGlvblBhcGVyID0gYXdhaXQgY3JlYXRlUXVlc3Rpb25QYXBlcihhcGlQYXlsb2FkKVxyXG4gICAgICBjb25zb2xlLmxvZyhcIlF1ZXN0aW9uIHBhcGVyIGNyZWF0ZWQ6XCIsIHF1ZXN0aW9uUGFwZXIpXHJcblxyXG4gICAgICAvLyBTaG93IGNyZWF0aW9uIHN1Y2Nlc3NcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIlF1ZXN0aW9uIFBhcGVyIENyZWF0ZWRcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTdGFydGluZyBQREYgZG93bmxvYWQuLi5cIixcclxuICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBEb3dubG9hZCB0aGUgUERGXHJcbiAgICAgIGNvbnN0IHBkZkJsb2IgPSBhd2FpdCBkb3dubG9hZFF1ZXN0aW9uUGFwZXIocXVlc3Rpb25QYXBlci5faWQsICdwZGYnKVxyXG5cclxuICAgICAgLy8gQ3JlYXRlIGRvd25sb2FkIGxpbmtcclxuICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwocGRmQmxvYilcclxuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKVxyXG4gICAgICBsaW5rLmhyZWYgPSB1cmxcclxuICAgICAgbGluay5kb3dubG9hZCA9IGAke2Zvcm1EYXRhLnRpdGxlLnJlcGxhY2UoL1xccysvZywgJ18nKX1fJHtEYXRlLm5vdygpfS5wZGZgXHJcbiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaylcclxuICAgICAgbGluay5jbGljaygpXHJcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluaylcclxuICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKVxyXG5cclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIlN1Y2Nlc3MhXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiUXVlc3Rpb24gcGFwZXIgZ2VuZXJhdGVkIGFuZCBkb3dubG9hZGVkIHN1Y2Nlc3NmdWxseSFcIixcclxuICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBSZXNldCB0byBmaXJzdCBzdGVwIGFuZCBjbGVhciBmb3JtIGRhdGEgYWZ0ZXIgYSBzaG9ydCBkZWxheVxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzZXRDdXJyZW50U3RlcCgwKVxyXG4gICAgICAgIHNldEZvcm1EYXRhKGluaXRpYWxGb3JtRGF0YSlcclxuICAgICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpXHJcbiAgICAgIH0sIDEwMDApIC8vIDEgc2Vjb25kIGRlbGF5IHRvIGVuc3VyZSB0b2FzdCBpcyB2aXNpYmxlXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpXHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdWJtaXR0aW5nIGZvcm06XCIsIGVycm9yKVxyXG5cclxuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiQW4gdW5rbm93biBlcnJvciBvY2N1cnJlZFwiO1xyXG4gICAgICBsZXQgZXJyb3JUaXRsZSA9IFwiRXJyb3JcIjtcclxuXHJcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XHJcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcclxuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgZXJyb3IgPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3I7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEhhbmRsZSBzcGVjaWZpYyBlcnJvciB0eXBlcyB3aXRoIGJldHRlciB0aXRsZXMgYW5kIG1lc3NhZ2VzXHJcbiAgICAgIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJVbmF1dGhvcml6ZWRcIikpIHtcclxuICAgICAgICBlcnJvclRpdGxlID0gXCJBdXRoZW50aWNhdGlvbiBFcnJvclwiO1xyXG4gICAgICAgIGVycm9yTWVzc2FnZSA9IFwiUGxlYXNlIGxvZyBpbiBhZ2FpbiB0byBjb250aW51ZS4gWW91ciBzZXNzaW9uIG1heSBoYXZlIGV4cGlyZWQuXCI7XHJcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKFwiTmV0d29ya1wiKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJmZXRjaFwiKSkge1xyXG4gICAgICAgIGVycm9yVGl0bGUgPSBcIk5ldHdvcmsgRXJyb3JcIjtcclxuICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIlBsZWFzZSBjaGVjayB5b3VyIGludGVybmV0IGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2Fpbi5cIjtcclxuICAgICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJ1bnVzZWQgcXVlc3Rpb25zIGF2YWlsYWJsZVwiKSkge1xyXG4gICAgICAgIGVycm9yVGl0bGUgPSBcIkluc3VmZmljaWVudCBRdWVzdGlvbnNcIjtcclxuICAgICAgICAvLyBFeHRyYWN0IG51bWJlcnMgZnJvbSB0aGUgZXJyb3IgbWVzc2FnZSBmb3IgYSBjbGVhcmVyIGV4cGxhbmF0aW9uXHJcbiAgICAgICAgY29uc3QgbWF0Y2ggPSBlcnJvck1lc3NhZ2UubWF0Y2goL09ubHkgKFxcZCspIHVudXNlZCBxdWVzdGlvbnMgYXZhaWxhYmxlXFwuIFJlcXVlc3RlZDogKFxcZCspLyk7XHJcbiAgICAgICAgaWYgKG1hdGNoKSB7XHJcbiAgICAgICAgICBjb25zdCBhdmFpbGFibGUgPSBtYXRjaFsxXTtcclxuICAgICAgICAgIGNvbnN0IHJlcXVlc3RlZCA9IG1hdGNoWzJdO1xyXG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gYE9ubHkgJHthdmFpbGFibGV9IHF1ZXN0aW9ucyBhcmUgYXZhaWxhYmxlIGZvciB0aGlzIHN1YmplY3QvdG9waWMsIGJ1dCB5b3UgcmVxdWVzdGVkICR7cmVxdWVzdGVkfSBxdWVzdGlvbnMuIFBsZWFzZSByZWR1Y2UgdGhlIG51bWJlciBvZiBxdWVzdGlvbnMgb3IgYWRkIG1vcmUgcXVlc3Rpb25zIHRvIHRoZSBkYXRhYmFzZS5gO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoXCJxdWVzdGlvbnMgYXZhaWxhYmxlXCIpKSB7XHJcbiAgICAgICAgZXJyb3JUaXRsZSA9IFwiUXVlc3Rpb24gQXZhaWxhYmlsaXR5IElzc3VlXCI7XHJcbiAgICAgICAgLy8gS2VlcCB0aGUgb3JpZ2luYWwgbWVzc2FnZVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBBbHdheXMgc2hvdyB0b2FzdCB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlNob3dpbmcgZXJyb3IgdG9hc3Q6XCIsIHsgZXJyb3JUaXRsZSwgZXJyb3JNZXNzYWdlIH0pO1xyXG5cclxuICAgICAgLy8gRm9yY2UgdGhlIHRvYXN0IHRvIHNob3cgYnkgY2xlYXJpbmcgZXhpc3RpbmcgdG9hc3RzIGFuZCB1c2luZyBhIHVuaXF1ZSBJRFxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIkF0dGVtcHRpbmcgdG8gc2hvdyB0b2FzdCBub3cuLi5cIik7XHJcblxyXG4gICAgICAgIC8vIENsZWFyIGFueSBleGlzdGluZyB0b2FzdHMgZmlyc3RcclxuICAgICAgICBkaXNtaXNzKCk7XHJcblxyXG4gICAgICAgIC8vIFdhaXQgYSBiaXQgbW9yZSB0aGVuIHNob3cgdGhlIGVycm9yIHRvYXN0XHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlNob3dpbmcgZXJyb3IgdG9hc3QgYWZ0ZXIgY2xlYXJpbmcuLi5cIik7XHJcblxyXG4gICAgICAgICAgLy8gVHJ5IHRoZSBob29rLWJhc2VkIHRvYXN0IHdpdGggdW5pcXVlIElEXHJcbiAgICAgICAgICBjb25zdCB0b2FzdElkID0gYGVycm9yLSR7RGF0ZS5ub3coKX1gO1xyXG4gICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICBpZDogdG9hc3RJZCxcclxuICAgICAgICAgICAgdGl0bGU6IGVycm9yVGl0bGUsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvck1lc3NhZ2UsXHJcbiAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIEFsc28gdHJ5IHRoZSBzdGFuZGFsb25lIHRvYXN0IGFzIGJhY2t1cFxyXG4gICAgICAgICAgc3RhbmRhbG9uZVRvYXN0KHtcclxuICAgICAgICAgICAgaWQ6IGBzdGFuZGFsb25lLSR7RGF0ZS5ub3coKX1gLFxyXG4gICAgICAgICAgICB0aXRsZTogZXJyb3JUaXRsZSxcclxuICAgICAgICAgICAgZGVzY3JpcHRpb246IGVycm9yTWVzc2FnZSxcclxuICAgICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgfSwgMjAwKTtcclxuICAgICAgfSwgMzAwKTsgLy8gSW5pdGlhbCBkZWxheSB0byBlbnN1cmUgYW55IGxvYWRpbmcgc3RhdGVzIGFyZSBjbGVhcmVkXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBzdGVwcyA9IFtcclxuICAgIHtcclxuICAgICAgdGl0bGU6IFwiUXVlc3Rpb24gVHlwZVwiLFxyXG4gICAgICBpY29uOiBcIkhlbHBDaXJjbGVcIixcclxuICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgPFF1ZXN0aW9uVHlwZVN0ZXAgXHJcbiAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9IFxyXG4gICAgICAgICAgdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfSBcclxuICAgICAgICAgIG9uTmV4dD17bmV4dFN0ZXB9IFxyXG4gICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgIG9uQmFjaz17cHJldlN0ZXB9XHJcbiAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgdGl0bGU6IFwiUXVlc3Rpb24gVHlwZVwiLFxyXG4gICAgICBpY29uOiBcIkhlbHBDaXJjbGVcIixcclxuICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgPFF1ZXN0aW9uVGl0bGVBbmREZXNjcmlwdGlvblN0ZXAgXHJcbiAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9IFxyXG4gICAgICAgICAgdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfSBcclxuICAgICAgICAgIG9uTmV4dD17bmV4dFN0ZXB9IFxyXG4gICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgIG9uQmFjaz17cHJldlN0ZXB9XHJcbiAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogXCJDb3Vyc2UgJiBTdWJqZWN0IFNlbGVjdGlvblwiLFxyXG4gICAgICBpY29uOiBcIkJvb2tPcGVuXCIsXHJcbiAgICAgIGNvbXBvbmVudDogKFxyXG4gICAgICAgIDxDb3Vyc2VTdWJqZWN0U3RlcCBcclxuICAgICAgICAgIGZvcm1EYXRhPXtmb3JtRGF0YX0gXHJcbiAgICAgICAgICB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9IFxyXG4gICAgICAgICAgb25OZXh0PXtuZXh0U3RlcH0gXHJcbiAgICAgICAgICBvblNraXA9e3NraXBTdGVwfVxyXG4gICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgIGJhY2tEaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgLz5cclxuICAgICAgKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHRpdGxlOiBcIlNlbGVjdCBEaWZmaWN1bHR5IExldmVsXCIsXHJcbiAgICAgIGljb246IFwiQmFyQ2hhcnQyXCIsXHJcbiAgICAgIGNvbXBvbmVudDogKFxyXG4gICAgICAgIDxEaWZmaWN1bHR5TGV2ZWxTdGVwIFxyXG4gICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfSBcclxuICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhPXt1cGRhdGVGb3JtRGF0YX0gXHJcbiAgICAgICAgICBvbk5leHQ9e25leHRTdGVwfSBcclxuICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICBvbkJhY2s9e3ByZXZTdGVwfVxyXG4gICAgICAgICAgYmFja0Rpc2FibGVkPXtjdXJyZW50U3RlcCA9PT0gMH1cclxuICAgICAgICAvPlxyXG4gICAgICApLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6IFwiUXVlc3Rpb24gU2VsZWN0aW9uIENyaXRlcmlhXCIsXHJcbiAgICAgIGljb246IFwiRmlsZVRleHRcIixcclxuICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgPFF1ZXN0aW9uU2VsZWN0aW9uU3RlcFxyXG4gICAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxyXG4gICAgICAgICAgdXBkYXRlRm9ybURhdGE9e3VwZGF0ZUZvcm1EYXRhfVxyXG4gICAgICAgICAgb25OZXh0PXtuZXh0U3RlcH1cclxuICAgICAgICAgIG9uU2tpcD17c2tpcFN0ZXB9XHJcbiAgICAgICAgICBvbkJhY2s9e3ByZXZTdGVwfVxyXG4gICAgICAgICAgYmFja0Rpc2FibGVkPXtjdXJyZW50U3RlcCA9PT0gMH1cclxuICAgICAgICAvPlxyXG4gICAgICApLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdGl0bGU6IFwiUGFwZXIgQ3VzdG9taXphdGlvblwiLFxyXG4gICAgICBpY29uOiBcIkZpbGVFZGl0XCIsXHJcbiAgICAgIGNvbXBvbmVudDogKFxyXG4gICAgICAgIDxQYXBlckN1c3RvbWl6YXRpb25TdGVwXHJcbiAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XHJcbiAgICAgICAgICB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9XHJcbiAgICAgICAgICBvbk5leHQ9e25leHRTdGVwfVxyXG4gICAgICAgICAgb25Ta2lwPXtza2lwU3RlcH1cclxuICAgICAgICAgIG9uQmFjaz17cHJldlN0ZXB9XHJcbiAgICAgICAgICBiYWNrRGlzYWJsZWQ9e2N1cnJlbnRTdGVwID09PSAwfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICksXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB0aXRsZTogXCJJbmNsdWRlIEFuc3dlcnM/XCIsXHJcbiAgICAgIGljb246IFwiQ2hlY2tTcXVhcmVcIixcclxuICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgPEluY2x1ZGVBbnN3ZXJzU3RlcCBcclxuICAgICAgICAgIGZvcm1EYXRhPXtmb3JtRGF0YX0gXHJcbiAgICAgICAgICB1cGRhdGVGb3JtRGF0YT17dXBkYXRlRm9ybURhdGF9IFxyXG4gICAgICAgICAgb25OZXh0PXtuZXh0U3RlcH0gXHJcbiAgICAgICAgICBvblNraXA9e3NraXBTdGVwfVxyXG4gICAgICAgICAgb25CYWNrPXtwcmV2U3RlcH1cclxuICAgICAgICAgIGJhY2tEaXNhYmxlZD17Y3VycmVudFN0ZXAgPT09IDB9XHJcbiAgICAgICAgLz5cclxuICAgICAgKSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHRpdGxlOiBcIkFjdGlvbnNcIixcclxuICAgICAgaWNvbjogXCJGaWxlT3V0cHV0XCIsXHJcbiAgICAgIGNvbXBvbmVudDogPEFjdGlvbnNTdGVwIGZvcm1EYXRhPXtmb3JtRGF0YX0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gaXNMb2FkaW5nPXtpc0dlbmVyYXRpbmd9IC8+LFxyXG4gICAgfSxcclxuICBdXHJcblxyXG4gIC8vIFRlc3QgdG9hc3QgZnVuY3Rpb25cclxuICBjb25zdCB0ZXN0VG9hc3QgPSAoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIlRlc3QgdG9hc3QgY2xpY2tlZFwiKTtcclxuICAgIHRvYXN0KHtcclxuICAgICAgdGl0bGU6IFwiVGVzdCBUb2FzdCAoSG9vaylcIixcclxuICAgICAgZGVzY3JpcHRpb246IFwiVGhpcyBpcyBhIHRlc3QgdG9hc3QgdXNpbmcgdGhlIGhvb2sgbWV0aG9kXCIsXHJcbiAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIEFsc28gdGVzdCBzdGFuZGFsb25lXHJcbiAgICBzdGFuZGFsb25lVG9hc3Qoe1xyXG4gICAgICB0aXRsZTogXCJUZXN0IFRvYXN0IChTdGFuZGFsb25lKVwiLFxyXG4gICAgICBkZXNjcmlwdGlvbjogXCJUaGlzIGlzIGEgdGVzdCB0b2FzdCB1c2luZyB0aGUgc3RhbmRhbG9uZSBtZXRob2RcIixcclxuICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gVGVzdCBlcnJvciB0b2FzdCBmdW5jdGlvblxyXG4gIGNvbnN0IHRlc3RFcnJvclRvYXN0ID0gKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJUZXN0IGVycm9yIHRvYXN0IGNsaWNrZWRcIik7XHJcbiAgICBjb25zdCBlcnJvclRpdGxlID0gXCJJbnN1ZmZpY2llbnQgUXVlc3Rpb25zXCI7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBcIk9ubHkgMiBxdWVzdGlvbnMgYXJlIGF2YWlsYWJsZSBmb3IgdGhpcyBzdWJqZWN0L3RvcGljLCBidXQgeW91IHJlcXVlc3RlZCAzIHF1ZXN0aW9ucy4gUGxlYXNlIHJlZHVjZSB0aGUgbnVtYmVyIG9mIHF1ZXN0aW9ucyBvciBhZGQgbW9yZSBxdWVzdGlvbnMgdG8gdGhlIGRhdGFiYXNlLlwiO1xyXG5cclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlNob3dpbmcgdGVzdCBlcnJvciB0b2FzdC4uLlwiKTtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBlcnJvclRpdGxlLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvck1lc3NhZ2UsXHJcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHN0YW5kYWxvbmVUb2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IGVycm9yVGl0bGUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGVycm9yTWVzc2FnZSxcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pO1xyXG4gICAgfSwgNTAwKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgey8qIFRlbXBvcmFyeSB0ZXN0IGJ1dHRvbnMgLSByZW1vdmUgYWZ0ZXIgdGVzdGluZyAqL31cclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIG9uQ2xpY2s9e3Rlc3RUb2FzdH1cclxuICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWRcIlxyXG4gICAgICAgIHN0eWxlPXt7IHBvc2l0aW9uOiAnZml4ZWQnLCB0b3A6ICcxMHB4JywgcmlnaHQ6ICcxMHB4JywgekluZGV4OiA5OTk5IH19XHJcbiAgICAgID5cclxuICAgICAgICBUZXN0IFRvYXN0XHJcbiAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8YnV0dG9uXHJcbiAgICAgICAgb25DbGljaz17dGVzdEVycm9yVG9hc3R9XHJcbiAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLW9yYW5nZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkXCJcclxuICAgICAgICBzdHlsZT17eyBwb3NpdGlvbjogJ2ZpeGVkJywgdG9wOiAnNjBweCcsIHJpZ2h0OiAnMTBweCcsIHpJbmRleDogOTk5OSB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgVGVzdCBFcnJvciBUb2FzdFxyXG4gICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgIDxTdGVwSW5kaWNhdG9yIGN1cnJlbnRTdGVwPXtjdXJyZW50U3RlcH0gc3RlcHM9e3N0ZXBzLm1hcCgoc3RlcCkgPT4gKHsgdGl0bGU6IHN0ZXAudGl0bGUsIGljb246IHN0ZXAuaWNvbiB9KSl9IC8+XHJcbiAgICAgIHtzdGVwc1tjdXJyZW50U3RlcF0uY29tcG9uZW50fVxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsImNyZWF0ZVF1ZXN0aW9uUGFwZXIiLCJkb3dubG9hZFF1ZXN0aW9uUGFwZXIiLCJ1c2VUb2FzdCIsInRvYXN0Iiwic3RhbmRhbG9uZVRvYXN0IiwiUXVlc3Rpb25UeXBlU3RlcCIsIkNvdXJzZVN1YmplY3RTdGVwIiwiRGlmZmljdWx0eUxldmVsU3RlcCIsIlF1ZXN0aW9uU2VsZWN0aW9uU3RlcCIsIlBhcGVyQ3VzdG9taXphdGlvblN0ZXAiLCJJbmNsdWRlQW5zd2Vyc1N0ZXAiLCJBY3Rpb25zU3RlcCIsIlN0ZXBJbmRpY2F0b3IiLCJRdWVzdGlvblRpdGxlQW5kRGVzY3JpcHRpb25TdGVwIiwiaW5pdGlhbEZvcm1EYXRhIiwicXVlc3Rpb25UeXBlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImNvdXJzZSIsInN1YmplY3QiLCJkaWZmaWN1bHR5TW9kZSIsImRpZmZpY3VsdHlMZXZlbHMiLCJlYXN5IiwibWVkaXVtIiwiaGFyZCIsIm51bWJlck9mUXVlc3Rpb25zIiwidG90YWxNYXJrcyIsImluY2x1ZGVBbnN3ZXJzIiwiZHVyYXRpb24iLCJpbnN0cnVjdGlvbnMiLCJ0b3BpY0lkIiwidW5kZWZpbmVkIiwiUXVlc3Rpb25QYXBlcldpemFyZCIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwiZGlzbWlzcyIsInVwZGF0ZUZvcm1EYXRhIiwiZGF0YSIsInByZXYiLCJuZXh0U3RlcCIsIk1hdGgiLCJtaW4iLCJzdGVwcyIsImxlbmd0aCIsInByZXZTdGVwIiwibWF4Iiwic2tpcFN0ZXAiLCJoYW5kbGVTdWJtaXQiLCJjb25zb2xlIiwibG9nIiwiYmFja2VuZFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImZpcmViYXNlVG9rZW4iLCJ0b2tlbiIsInN1YnN0cmluZyIsInRyaW0iLCJ2YXJpYW50IiwiYXBpUGF5bG9hZCIsImV4YW1UeXBlIiwibWF4UXVlc3Rpb25zIiwiY3VzdG9taXNlIiwiY3VzdG9tRGlmZmljdWx0eSIsImVhc3lQZXJjZW50YWdlIiwibWVkaXVtUGVyY2VudGFnZSIsImhhcmRQZXJjZW50YWdlIiwicXVlc3Rpb25QYXBlciIsInBkZkJsb2IiLCJfaWQiLCJ1cmwiLCJ3aW5kb3ciLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiRGF0ZSIsInJlcGxhY2UiLCJub3ciLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwic2V0VGltZW91dCIsImVycm9yIiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JUaXRsZSIsIkVycm9yIiwibWVzc2FnZSIsImluY2x1ZGVzIiwibWF0Y2giLCJhdmFpbGFibGUiLCJyZXF1ZXN0ZWQiLCJ0b2FzdElkIiwiaWQiLCJpY29uIiwiY29tcG9uZW50Iiwib25OZXh0Iiwib25Ta2lwIiwib25CYWNrIiwiYmFja0Rpc2FibGVkIiwib25TdWJtaXQiLCJpc0xvYWRpbmciLCJ0ZXN0VG9hc3QiLCJ0ZXN0RXJyb3JUb2FzdCIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdHlsZSIsInBvc2l0aW9uIiwidG9wIiwicmlnaHQiLCJ6SW5kZXgiLCJtYXAiLCJzdGVwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});