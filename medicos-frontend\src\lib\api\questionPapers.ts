const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

/**
 * Interface for creating a question paper
 */
export interface CreateQuestionPaperDto {
  title: string;
  description?: string;
  maxQuestions?: number;
  subject: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  instructions?: string;
  examType: string;
  customise?: {
    customDifficulty: {
      easyPercentage: number;
      mediumPercentage: number;
      hardPercentage: number;
    };
    numberOfQuestions: number;
    totalMarks: number;
    duration: number;
    includeAnswers: boolean;
  };
}

/**
 * Interface for question paper response
 */
export interface QuestionPaperResponse {
  _id: string;
  title: string;
  description?: string;
  subjectId: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  withAnswers: boolean;
  instructions?: string;
  examType: string;
  difficultyMode: string;
  questions: Array<{
    _id: string;
    content: string;
    options: string[];
    answer: string;
    difficulty: string;
    type: string;
    marks: number;
  }>;
  generatedBy: string;
  collegeId?: string;
  status: string;
  sections: Array<{
    name: string;
    description: string;
    order: number;
    sectionMarks: number;
    questions: Array<{
      question: any;
      order: number;
    }>;
  }>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create a new question paper
 * @param questionPaperData The question paper data
 * @returns The created question paper
 */
export async function createQuestionPaper(questionPaperData: CreateQuestionPaperDto): Promise<QuestionPaperResponse> {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify(questionPaperData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating question paper:", error);
    throw error;
  }
}

/**
 * Download a question paper as PDF
 * @param questionPaperId The question paper ID
 * @param format The format (pdf or docx)
 * @returns The file blob
 */
export async function downloadQuestionPaper(questionPaperId: string, format: 'pdf' | 'docx' = 'pdf'): Promise<Blob> {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}/download?format=${format}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }

    return await response.blob();
  } catch (error) {
    console.error("Error downloading question paper:", error);
    throw error;
  }
}

/**
 * Get all question papers
 * @returns List of question papers
 */
export async function getQuestionPapers(): Promise<QuestionPaperResponse[]> {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question papers:", error);
    throw error;
  }
}

/**
 * Get a specific question paper by ID
 * @param questionPaperId The question paper ID
 * @returns The question paper
 */
export async function getQuestionPaper(questionPaperId: string): Promise<QuestionPaperResponse> {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question paper:", error);
    throw error;
  }
}
