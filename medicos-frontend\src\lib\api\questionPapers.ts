const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

/**
 * Interface for creating a question paper
 */
export interface CreateQuestionPaperDto {
  title: string;
  description?: string;
  maxQuestions?: number;
  subject: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  instructions?: string;
  examType: string;
  customise?: {
    customDifficulty: {
      easyPercentage: number;
      mediumPercentage: number;
      hardPercentage: number;
    };
    numberOfQuestions: number;
    totalMarks: number;
    duration: number;
    includeAnswers: boolean;
  };
}

/**
 * Interface for question paper response
 */
export interface QuestionPaperResponse {
  _id: string;
  title: string;
  description?: string;
  subjectId: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  withAnswers: boolean;
  instructions?: string;
  examType: string;
  difficultyMode: string;
  questions: Array<{
    _id: string;
    content: string;
    options: string[];
    answer: string;
    difficulty: string;
    type: string;
    marks: number;
  }>;
  generatedBy: string;
  collegeId?: string;
  status: string;
  sections: Array<{
    name: string;
    description: string;
    order: number;
    sectionMarks: number;
    questions: Array<{
      question: any;
      order: number;
    }>;
  }>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Get authentication headers with proper token
 */
function getAuthHeaders(): Record<string, string> {
  // Try different token storage keys used in the codebase
  const backendToken = localStorage.getItem("backendToken");
  const firebaseToken = localStorage.getItem("firebaseToken");
  const token = localStorage.getItem("token");

  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };

  // Prefer backend token, then firebase token, then generic token
  if (backendToken) {
    headers["Authorization"] = `Bearer ${backendToken}`;
  } else if (firebaseToken) {
    headers["Authorization"] = `Bearer ${firebaseToken}`;
  } else if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  } else {
    throw new Error("Authentication required - Please log in again. No valid authentication token found.");
  }

  return headers;
}

/**
 * Create a new question paper
 * @param questionPaperData The question paper data
 * @returns The created question paper
 */
export async function createQuestionPaper(questionPaperData: CreateQuestionPaperDto): Promise<QuestionPaperResponse> {
  try {
    const headers = getAuthHeaders();

    console.log("Creating question paper with data:", questionPaperData);
    console.log("Using headers:", { ...headers, Authorization: headers.Authorization ? `Bearer ${headers.Authorization.substring(0, 20)}...` : 'None' });

    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "POST",
      headers,
      body: JSON.stringify(questionPaperData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API Error Response:", errorData);

      // Provide more specific error messages based on status code
      let errorMessage = errorData.message || `Error: ${response.status} - ${response.statusText}`;

      switch (response.status) {
        case 401:
          errorMessage = "Authentication required - Please log in again.";
          break;
        case 403:
          errorMessage = "Access denied - You don't have permission to perform this action.";
          break;
        case 404:
          errorMessage = "Resource not found - The requested item could not be found.";
          break;
        case 429:
          errorMessage = "Too many requests - Please wait a moment before trying again.";
          break;
        case 500:
          errorMessage = "Server error - Please try again later.";
          break;
        case 503:
          errorMessage = "Service unavailable - The server is temporarily down.";
          break;
        default:
          if (response.status >= 400 && response.status < 500) {
            errorMessage = errorData.message || "Invalid request - Please check your input and try again.";
          } else if (response.status >= 500) {
            errorMessage = "Server error - Please try again later.";
          }
      }

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating question paper:", error);
    throw error;
  }
}

/**
 * Download a question paper as PDF
 * @param questionPaperId The question paper ID
 * @param format The format (pdf or docx)
 * @returns The file blob
 */
export async function downloadQuestionPaper(questionPaperId: string, format: 'pdf' | 'docx' = 'pdf'): Promise<Blob> {
  try {
    const headers = getAuthHeaders();
    delete headers["Content-Type"]; // Remove content-type for blob response

    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}/download?format=${format}`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status} - ${response.statusText}`);
    }

    return await response.blob();
  } catch (error) {
    console.error("Error downloading question paper:", error);
    throw error;
  }
}

/**
 * Get all question papers
 * @returns List of question papers
 */
export async function getQuestionPapers(): Promise<QuestionPaperResponse[]> {
  try {
    const headers = getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question papers:", error);
    throw error;
  }
}

/**
 * Get a specific question paper by ID
 * @param questionPaperId The question paper ID
 * @returns The question paper
 */
export async function getQuestionPaper(questionPaperId: string): Promise<QuestionPaperResponse> {
  try {
    const headers = getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question paper:", error);
    throw error;
  }
}
