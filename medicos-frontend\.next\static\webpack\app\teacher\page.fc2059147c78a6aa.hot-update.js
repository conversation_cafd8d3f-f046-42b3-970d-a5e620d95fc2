"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API Error Response:\", errorData);\n            // Provide more specific error messages based on status code\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            switch(response.status){\n                case 401:\n                    errorMessage = \"Authentication required - Please log in again.\";\n                    break;\n                case 403:\n                    errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                    break;\n                case 404:\n                    errorMessage = \"Resource not found - The requested item could not be found.\";\n                    break;\n                case 429:\n                    errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                    break;\n                case 500:\n                    errorMessage = \"Server error - Please try again later.\";\n                    break;\n                case 503:\n                    errorMessage = \"Service unavailable - The server is temporarily down.\";\n                    break;\n                default:\n                    if (response.status >= 400 && response.status < 500) {\n                        errorMessage = errorData.message || \"Invalid request - Please check your input and try again.\";\n                    } else if (response.status >= 500) {\n                        errorMessage = \"Server error - Please try again later.\";\n                    }\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});