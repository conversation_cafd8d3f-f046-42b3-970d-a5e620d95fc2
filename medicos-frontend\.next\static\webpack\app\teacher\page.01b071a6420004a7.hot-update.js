"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - no valid token found\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API Error Response:\", errorData);\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});