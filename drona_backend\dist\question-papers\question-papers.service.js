"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QuestionPapersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPapersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_paper_schema_1 = require("../schema/question-paper.schema");
const question_schema_1 = require("../schema/question.schema");
const subject_schema_1 = require("../schema/subject.schema");
const tracking_service_1 = require("../common/services/tracking.service");
const question_usage_service_1 = require("../question-usage/question-usage.service");
const create_question_paper_dto_1 = require("./dto/create-question-paper.dto");
const fs = require("fs");
const path = require("path");
const PDFDocument = require("pdfkit");
const docx_1 = require("docx");
let QuestionPapersService = QuestionPapersService_1 = class QuestionPapersService {
    constructor(questionPaperModel, questionModel, subjectModel, trackingService, questionUsageService) {
        this.questionPaperModel = questionPaperModel;
        this.questionModel = questionModel;
        this.subjectModel = subjectModel;
        this.trackingService = trackingService;
        this.questionUsageService = questionUsageService;
        this.logger = new common_1.Logger(QuestionPapersService_1.name);
    }
    async createUnified(createQuestionPaperDto, user) {
        try {
            if (user.role !== 'teacher' && user.role !== 'superAdmin') {
                throw new common_1.BadRequestException('Only teachers and super admins can create question papers.');
            }
            if (user.role === 'teacher' && !user.collegeId) {
                throw new common_1.BadRequestException('Teacher must be associated with a college');
            }
            const isCustomized = !!createQuestionPaperDto.customise;
            const subject = await this.resolveSubject(createQuestionPaperDto.subject);
            if (!subject) {
                throw new common_1.BadRequestException('Invalid subject provided');
            }
            await this.checkGenerationLimits(user, subject._id.toString());
            const questionQuery = {
                subjectId: subject._id,
                status: 'active',
                reviewStatus: 'approved',
            };
            if (createQuestionPaperDto.topicId) {
                questionQuery.topicId = createQuestionPaperDto.topicId;
            }
            const availableQuestions = await this.questionModel.find(questionQuery);
            if (availableQuestions.length === 0) {
                throw new common_1.BadRequestException('No questions available for the specified criteria');
            }
            const unusedQuestions = await this.filterUnusedQuestions(availableQuestions, user, subject._id.toString(), createQuestionPaperDto.topicId?.toString());
            if (unusedQuestions.length === 0) {
                throw new common_1.BadRequestException('No unused questions available. Please add more questions or wait for the duplicate prevention period to expire.');
            }
            let selectedQuestions;
            let sections;
            let totalMarks;
            let duration;
            let withAnswers;
            if (isCustomized) {
                const customConfig = createQuestionPaperDto.customise;
                if (unusedQuestions.length < customConfig.numberOfQuestions) {
                    throw new common_1.BadRequestException(`Only ${unusedQuestions.length} unused questions available. Requested: ${customConfig.numberOfQuestions}`);
                }
                selectedQuestions = await this.selectQuestionsByDifficulty(unusedQuestions, customConfig.numberOfQuestions, 'custom', customConfig.customDifficulty);
                sections = [
                    {
                        name: 'Section A',
                        description: 'All Questions',
                        order: 1,
                        sectionMarks: customConfig.totalMarks,
                        questions: selectedQuestions.map((q, index) => ({
                            questionId: q._id,
                            order: index + 1,
                        })),
                    },
                ];
                totalMarks = customConfig.totalMarks;
                duration = customConfig.duration;
                withAnswers = customConfig.includeAnswers;
            }
            else {
                const numberOfQuestions = Math.min(50, unusedQuestions.length);
                selectedQuestions = await this.selectQuestionsByDifficulty(unusedQuestions, numberOfQuestions, 'auto');
                sections = [
                    {
                        name: 'Section A',
                        description: 'All Questions',
                        order: 1,
                        sectionMarks: createQuestionPaperDto.totalMarks,
                        questions: selectedQuestions.map((q, index) => ({
                            questionId: q._id,
                            order: index + 1,
                        })),
                    },
                ];
                totalMarks = createQuestionPaperDto.totalMarks;
                duration = createQuestionPaperDto.duration;
                withAnswers = false;
            }
            const questionPaperData = {
                title: createQuestionPaperDto.title,
                description: createQuestionPaperDto.description,
                subjectId: subject._id,
                topicId: createQuestionPaperDto.topicId,
                totalMarks,
                duration,
                withAnswers,
                instructions: createQuestionPaperDto.instructions,
                sections,
                questions: selectedQuestions.map((q) => q._id),
                generatedBy: user._id,
                status: 'active',
                examType: createQuestionPaperDto.examType || create_question_paper_dto_1.ExamType.CUSTOM,
                difficultyMode: isCustomized ? 'custom' : 'auto',
            };
            if (user.collegeId) {
                questionPaperData.collegeId = user.collegeId;
            }
            const questionPaper = new this.questionPaperModel(questionPaperData);
            const savedPaper = await questionPaper.save();
            if (user.role === 'teacher' && user.collegeId) {
                await this.recordQuestionUsage(savedPaper, selectedQuestions, user);
            }
            const populatedPaper = await this.questionPaperModel
                .findById(savedPaper._id)
                .populate('subjectId', 'name description')
                .populate('topicId', 'name description')
                .populate('questions', 'content options answer difficulty type marks explanation imageUrls')
                .exec();
            if (populatedPaper && populatedPaper.sections) {
                populatedPaper.sections = populatedPaper.sections.map(section => {
                    const mappedQuestions = section.questions.map(sectionQuestion => {
                        const fullQuestion = populatedPaper.questions.find(q => {
                            const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                            const qIdStr = qId ? qId.toString() : '';
                            const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                            return qIdStr === sectionQuestionIdStr;
                        });
                        return {
                            questionId: sectionQuestion.questionId,
                            order: sectionQuestion.order,
                            question: fullQuestion
                        };
                    });
                    return {
                        ...section,
                        questions: mappedQuestions
                    };
                });
            }
            this.logger.log(`Unified question paper created: ${savedPaper.title} (ID: ${savedPaper._id})`);
            return populatedPaper;
        }
        catch (error) {
            this.logger.error(`Error creating unified question paper: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findAll(user) {
        let query = {};
        const populateFields = ['subjectId', 'topicId', 'questions'];
        switch (user.role) {
            case 'superAdmin':
                query = {};
                populateFields.push('collegeId', 'generatedBy');
                break;
            case 'collegeAdmin':
                query = { collegeId: user.collegeId };
                populateFields.push('generatedBy');
                break;
            case 'teacher':
                query = { generatedBy: user._id };
                break;
            default:
                throw new common_1.BadRequestException('Invalid user role');
        }
        const questionPapers = await this.questionPaperModel.find(query).populate(populateFields).exec();
        return questionPapers.map(questionPaper => {
            if (questionPaper && questionPaper.sections) {
                questionPaper.sections = questionPaper.sections.map(section => {
                    const mappedQuestions = section.questions.map(sectionQuestion => {
                        const fullQuestion = questionPaper.questions.find(q => {
                            const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                            const qIdStr = qId ? qId.toString() : '';
                            const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                            return qIdStr === sectionQuestionIdStr;
                        });
                        return {
                            questionId: sectionQuestion.questionId,
                            order: sectionQuestion.order,
                            question: fullQuestion
                        };
                    });
                    return {
                        ...section,
                        questions: mappedQuestions
                    };
                });
            }
            return questionPaper;
        });
    }
    async findOne(id, user) {
        const query = { _id: id };
        const populateFields = ['subjectId', 'topicId', 'questions'];
        switch (user.role) {
            case 'superAdmin':
                populateFields.push('collegeId', 'generatedBy');
                break;
            case 'collegeAdmin':
                query.collegeId = user.collegeId;
                populateFields.push('generatedBy');
                break;
            case 'teacher':
                query.generatedBy = user._id;
                break;
            default:
                throw new common_1.BadRequestException('Invalid user role');
        }
        const questionPaper = await this.questionPaperModel
            .findOne(query)
            .populate(populateFields)
            .exec();
        if (!questionPaper) {
            throw new common_1.NotFoundException(`Question paper with ID ${id} not found or access denied`);
        }
        if (questionPaper && questionPaper.sections) {
            questionPaper.sections = questionPaper.sections.map(section => {
                const mappedQuestions = section.questions.map(sectionQuestion => {
                    const fullQuestion = questionPaper.questions.find(q => {
                        const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                        const qIdStr = qId ? qId.toString() : '';
                        const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                        return qIdStr === sectionQuestionIdStr;
                    });
                    return {
                        questionId: sectionQuestion.questionId,
                        order: sectionQuestion.order,
                        question: fullQuestion
                    };
                });
                return {
                    ...section,
                    questions: mappedQuestions
                };
            });
        }
        return questionPaper;
    }
    async update(id, updateQuestionPaperDto, user) {
        const questionPaper = await this.findOne(id, user);
        if (user.role === 'teacher') {
            const allowedFields = ['title'];
            const updateData = {};
            for (const field of allowedFields) {
                if (updateQuestionPaperDto[field] !== undefined) {
                    updateData[field] = updateQuestionPaperDto[field];
                }
            }
            if (Object.keys(updateData).length === 0) {
                throw new common_1.BadRequestException('Teachers can only update the title of question papers');
            }
            return await this.questionPaperModel
                .findByIdAndUpdate(id, updateData, { new: true })
                .exec();
        }
        return await this.questionPaperModel
            .findByIdAndUpdate(id, updateQuestionPaperDto, { new: true })
            .exec();
    }
    async checkDownloadLimits(user, questionPaper) {
        if (user.role === 'superAdmin') {
            return;
        }
        if (user.collegeId) {
            let limitDoc = await this.questionPaperModel
                .findOne({
                collegeId: user.collegeId,
                subjectId: questionPaper.subjectId,
                type: 'limit',
            })
                .exec();
            if (!limitDoc) {
                limitDoc = await this.questionPaperModel
                    .findOne({
                    collegeId: user.collegeId,
                    type: 'limit',
                    subjectId: { $exists: false },
                })
                    .exec();
            }
            if (limitDoc?.maxDownloads) {
                const downloadStats = await this.trackingService.getTeacherDownloadStats(user._id, {
                    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                    endDate: new Date(),
                });
                const totalDownloads = downloadStats.reduce((sum, stat) => sum + stat.totalDownloads, 0);
                if (totalDownloads >= limitDoc.maxDownloads) {
                    throw new common_1.BadRequestException(`You have reached the maximum number of downloads (${limitDoc.maxDownloads}) allowed for this month`);
                }
            }
        }
    }
    async download(id, format = 'pdf', user) {
        try {
            const questionPaper = await this.findOne(id, user);
            if (user) {
                await this.checkDownloadLimits(user, questionPaper);
            }
            const uploadsDir = path.join(process.cwd(), 'uploads');
            if (!fs.existsSync(uploadsDir)) {
                fs.mkdirSync(uploadsDir, { recursive: true });
            }
            const fileName = `${questionPaper.title.replace(/\s+/g, '_')}_${Date.now()}.${format}`;
            const filePath = path.join(uploadsDir, fileName);
            if (format === 'pdf') {
                await this.generatePDF(questionPaper, filePath);
            }
            else if (format === 'docx') {
                await this.generateDOCX(questionPaper, filePath);
            }
            else {
                throw new common_1.BadRequestException(`Unsupported format: ${format}`);
            }
            this.logger.log(`Generated ${format.toUpperCase()} file at: ${filePath}`);
            return filePath;
        }
        catch (error) {
            this.logger.error(`Error generating ${format} file: ${error.message}`, error.stack);
            throw error;
        }
    }
    async setQuestionLimit(setQuestionLimitDto) {
        const updateData = {};
        if (setQuestionLimitDto.maxQuestions) {
            updateData.maxQuestions = setQuestionLimitDto.maxQuestions;
        }
        if (setQuestionLimitDto.maxGeneration) {
            updateData.maxGeneration = setQuestionLimitDto.maxGeneration;
        }
        if (setQuestionLimitDto.maxDownloads) {
            updateData.maxDownloads = setQuestionLimitDto.maxDownloads;
        }
        const query = {
            collegeId: setQuestionLimitDto.collegeId,
            type: 'limit',
        };
        if (setQuestionLimitDto.subjectId) {
            query.subjectId = setQuestionLimitDto.subjectId;
        }
        await this.questionPaperModel
            .updateOne(query, { $set: updateData }, { upsert: true })
            .exec();
    }
    isBase64Image(str) {
        if (!str || typeof str !== 'string')
            return false;
        const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
        if (dataUrlPattern.test(str))
            return true;
        const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
        return str.length > 100 && base64Pattern.test(str);
    }
    extractImagesFromText(text) {
        if (!text)
            return { cleanText: text, images: [] };
        const images = [];
        let cleanText = text;
        let imageCounter = 0;
        const dataUrlPattern = /data:image\/([^;]+);base64,([A-Za-z0-9+/]+=*)/g;
        let match;
        while ((match = dataUrlPattern.exec(text)) !== null) {
            const [fullMatch, format, base64Data] = match;
            try {
                const buffer = Buffer.from(base64Data, 'base64');
                images.push({
                    id: `image_${imageCounter++}`,
                    data: buffer,
                    format: format.toLowerCase()
                });
                cleanText = cleanText.replace(fullMatch, `[Image ${imageCounter}]`);
            }
            catch (error) {
                this.logger.warn(`Failed to process base64 image: ${error.message}`);
            }
        }
        const base64Pattern = /[A-Za-z0-9+/]{100,}={0,2}/g;
        const base64Matches = cleanText.match(base64Pattern);
        if (base64Matches) {
            base64Matches.forEach((base64String) => {
                if (this.isBase64Image(base64String)) {
                    try {
                        const buffer = Buffer.from(base64String, 'base64');
                        let format = 'jpeg';
                        if (buffer[0] === 0x89 && buffer[1] === 0x50)
                            format = 'png';
                        else if (buffer[0] === 0xFF && buffer[1] === 0xD8)
                            format = 'jpeg';
                        else if (buffer[0] === 0x47 && buffer[1] === 0x49)
                            format = 'gif';
                        images.push({
                            id: `image_${imageCounter++}`,
                            data: buffer,
                            format
                        });
                        cleanText = cleanText.replace(base64String, `[Image ${imageCounter}]`);
                    }
                    catch (error) {
                        this.logger.warn(`Failed to process standalone base64 image: ${error.message}`);
                    }
                }
            });
        }
        cleanText = cleanText.replace(/\s+/g, ' ').trim();
        return { cleanText, images };
    }
    async generatePDF(questionPaper, filePath) {
        return new Promise((resolve, reject) => {
            try {
                const doc = new PDFDocument();
                const stream = fs.createWriteStream(filePath);
                doc.pipe(stream);
                doc.fontSize(20).text(questionPaper.title, { align: 'center' });
                doc.moveDown();
                if (questionPaper.subjectId &&
                    typeof questionPaper.subjectId === 'object' &&
                    'name' in questionPaper.subjectId) {
                    doc
                        .fontSize(14)
                        .text(`Subject: ${questionPaper.subjectId.name}`, {
                        align: 'center',
                    });
                    doc.moveDown();
                }
                if (questionPaper.instructions) {
                    doc.fontSize(12).text('Instructions:', { underline: true });
                    doc.fontSize(10).text(questionPaper.instructions);
                    doc.moveDown();
                }
                doc.fontSize(12).text(`Duration: ${questionPaper.duration} minutes`);
                doc.text(`Total Marks: ${questionPaper.totalMarks}`);
                doc.moveDown();
                doc.fontSize(14).text('Questions:');
                doc.moveDown();
                questionPaper.questions.forEach((question, index) => {
                    const questionResult = this.extractImagesFromText(question.content);
                    doc.fontSize(10).text(`${index + 1}. ${questionResult.cleanText}`);
                    questionResult.images.forEach((image) => {
                        try {
                            doc.moveDown(0.3);
                            doc.image(image.data, {
                                fit: [250, 120]
                            });
                            doc.moveDown(0.3);
                        }
                        catch (error) {
                            this.logger.warn(`Failed to add question image: ${error.message}`);
                            doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
                        }
                    });
                    if (question.imageUrls && Array.isArray(question.imageUrls) && question.imageUrls.length > 0) {
                        question.imageUrls.forEach((imageUrl) => {
                            if (this.isBase64Image(imageUrl)) {
                                try {
                                    const imageResult = this.extractImagesFromText(imageUrl);
                                    imageResult.images.forEach((image) => {
                                        doc.moveDown(0.3);
                                        doc.image(image.data, {
                                            fit: [250, 120]
                                        });
                                        doc.moveDown(0.3);
                                    });
                                }
                                catch (error) {
                                    this.logger.warn(`Failed to add imageUrl image: ${error.message}`);
                                    doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
                                }
                            }
                            else {
                                doc.fontSize(8).fillColor('blue').text(`Image: ${imageUrl}`).fillColor('black');
                                doc.moveDown(0.2);
                            }
                        });
                    }
                    if (question.options &&
                        Array.isArray(question.options) &&
                        question.options.length > 0) {
                        doc.moveDown(0.5);
                        question.options.forEach((option, optIndex) => {
                            const optionLabel = String.fromCharCode(97 + optIndex);
                            const optionResult = this.extractImagesFromText(option);
                            doc.fontSize(9).text(`    ${optionLabel}) ${optionResult.cleanText}`);
                            optionResult.images.forEach((image) => {
                                try {
                                    doc.moveDown(0.2);
                                    const currentX = doc.x + 20;
                                    doc.image(image.data, currentX, doc.y, {
                                        fit: [200, 100]
                                    });
                                    doc.moveDown(0.2);
                                }
                                catch (error) {
                                    this.logger.warn(`Failed to add option image: ${error.message}`);
                                    doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
                                }
                            });
                        });
                    }
                    if (questionPaper.withAnswers && question.answer) {
                        doc.moveDown(0.3);
                        const answerResult = this.extractImagesFromText(question.answer);
                        doc
                            .fillColor('blue')
                            .fontSize(9)
                            .text(`    Answer: ${answerResult.cleanText}`)
                            .fillColor('black');
                        answerResult.images.forEach((image) => {
                            try {
                                doc.moveDown(0.2);
                                const currentX = doc.x + 20;
                                doc.image(image.data, currentX, doc.y, {
                                    fit: [200, 100]
                                });
                                doc.moveDown(0.2);
                            }
                            catch (error) {
                                this.logger.warn(`Failed to add answer image: ${error.message}`);
                                doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
                            }
                        });
                    }
                    doc.moveDown();
                });
                doc.end();
                stream.on('finish', () => {
                    resolve();
                });
                stream.on('error', (err) => {
                    reject(err);
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async generateDOCX(questionPaper, filePath) {
        try {
            const sections = [];
            sections.push({
                properties: {},
                children: [
                    new docx_1.Paragraph({
                        text: questionPaper.title,
                        heading: docx_1.HeadingLevel.HEADING_1,
                        alignment: docx_1.AlignmentType.CENTER,
                    }),
                ],
            });
            if (questionPaper.subjectId &&
                typeof questionPaper.subjectId === 'object' &&
                'name' in questionPaper.subjectId) {
                sections[0].children.push(new docx_1.Paragraph({
                    text: `Subject: ${questionPaper.subjectId.name}`,
                    alignment: docx_1.AlignmentType.CENTER,
                }));
            }
            sections[0].children.push(new docx_1.Paragraph({
                text: `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`,
                alignment: docx_1.AlignmentType.LEFT,
                spacing: {
                    before: 400,
                    after: 200,
                },
            }));
            if (questionPaper.instructions) {
                sections[0].children.push(new docx_1.Paragraph({
                    text: 'Instructions:',
                    heading: docx_1.HeadingLevel.HEADING_2,
                }), new docx_1.Paragraph({
                    text: questionPaper.instructions,
                    spacing: {
                        after: 200,
                    },
                }));
            }
            sections[0].children.push(new docx_1.Paragraph({
                text: 'Questions:',
                heading: docx_1.HeadingLevel.HEADING_2,
                spacing: {
                    before: 200,
                    after: 200,
                },
            }));
            questionPaper.questions.forEach((question, index) => {
                const questionResult = this.extractImagesFromText(question.content);
                sections[0].children.push(new docx_1.Paragraph({
                    text: `${index + 1}. ${questionResult.cleanText}`,
                    spacing: {
                        before: 200,
                        after: 100,
                    },
                }));
                if (questionResult.images.length > 0) {
                    sections[0].children.push(new docx_1.Paragraph({
                        text: `    [${questionResult.images.length} image(s) - see PDF version for images]`,
                        spacing: {
                            after: 100,
                        },
                    }));
                }
                if (question.options &&
                    Array.isArray(question.options) &&
                    question.options.length > 0) {
                    question.options.forEach((option, optIndex) => {
                        const optionLabel = String.fromCharCode(97 + optIndex);
                        const optionResult = this.extractImagesFromText(option);
                        sections[0].children.push(new docx_1.Paragraph({
                            text: `    ${optionLabel}) ${optionResult.cleanText}`,
                            indent: {
                                left: 720,
                            },
                        }));
                        if (optionResult.images.length > 0) {
                            sections[0].children.push(new docx_1.Paragraph({
                                text: `        [${optionResult.images.length} image(s) - see PDF version for images]`,
                                indent: {
                                    left: 720,
                                },
                            }));
                        }
                    });
                }
                if (questionPaper.withAnswers && question.answer) {
                    const answerResult = this.extractImagesFromText(question.answer);
                    sections[0].children.push(new docx_1.Paragraph({
                        children: [
                            new docx_1.TextRun({
                                text: `    Answer: ${answerResult.cleanText}`,
                                color: '0000FF',
                                bold: true,
                            }),
                        ],
                        indent: {
                            left: 720,
                        },
                    }));
                    if (answerResult.images.length > 0) {
                        sections[0].children.push(new docx_1.Paragraph({
                            text: `        [${answerResult.images.length} image(s) - see PDF version for images]`,
                            indent: {
                                left: 720,
                            },
                        }));
                    }
                }
            });
            const doc = new docx_1.Document({
                sections,
            });
            const buffer = await docx_1.Packer.toBuffer(doc);
            fs.writeFileSync(filePath, buffer);
        }
        catch (error) {
            this.logger.error(`Error generating DOCX: ${error.message}`, error.stack);
            throw error;
        }
    }
    async resolveSubject(subjectInput) {
        if (mongoose_2.Types.ObjectId.isValid(subjectInput)) {
            const subject = await this.subjectModel.findById(subjectInput);
            if (subject)
                return subject;
        }
        const subject = await this.findSubjectByShortCode(subjectInput);
        if (subject)
            return subject;
        const exactMatch = await this.subjectModel.findOne({
            name: { $regex: new RegExp(`^${subjectInput}$`, 'i') },
        });
        if (exactMatch)
            return exactMatch;
        return null;
    }
    async findSubjectByShortCode(shortCode) {
        const subjectMappings = {
            [create_question_paper_dto_1.SubjectShortCode.PHYSICS]: ['physics', 'phy'],
            [create_question_paper_dto_1.SubjectShortCode.CHEMISTRY]: ['chemistry', 'chem'],
            [create_question_paper_dto_1.SubjectShortCode.BIOLOGY]: ['biology', 'bio'],
            [create_question_paper_dto_1.SubjectShortCode.MATHEMATICS]: ['mathematics', 'math'],
            [create_question_paper_dto_1.SubjectShortCode.MATH]: ['mathematics', 'math'],
            [create_question_paper_dto_1.SubjectShortCode.PHY]: ['physics', 'phy'],
            [create_question_paper_dto_1.SubjectShortCode.CHEM]: ['chemistry', 'chem'],
            [create_question_paper_dto_1.SubjectShortCode.BIO]: ['biology', 'bio'],
        };
        const searchTerms = subjectMappings[shortCode] || [shortCode];
        for (const term of searchTerms) {
            const subject = await this.subjectModel.findOne({
                name: { $regex: new RegExp(term, 'i') },
            });
            if (subject)
                return subject;
        }
        return null;
    }
    async recordQuestionUsage(questionPaper, selectedQuestions, user) {
        try {
            const usageData = selectedQuestions.map((question) => ({
                collegeId: user.collegeId,
                questionId: question._id.toString(),
                questionPaperId: questionPaper._id.toString(),
                usedBy: user._id,
                subjectId: questionPaper.subjectId.toString(),
                topicId: questionPaper.topicId?.toString(),
                metadata: {
                    examType: questionPaper.examType,
                    difficulty: question.difficulty,
                    marks: question.marks || 1,
                },
            }));
            const result = await this.questionUsageService.recordMultipleQuestionUsage(usageData);
            this.logger.log(`Recorded question usage: ${result.recorded} recorded, ${result.skipped} skipped for paper ${questionPaper._id}`);
        }
        catch (error) {
            this.logger.error(`Failed to record question usage for paper ${questionPaper._id}: ${error.message}`, error.stack);
        }
    }
    async checkGenerationLimits(user, subjectId) {
        const limitDoc = await this.questionPaperModel
            .findOne({
            collegeId: user.collegeId,
            subjectId,
            type: 'limit',
        })
            .exec();
        if (limitDoc?.maxQuestions) {
            const existingPapers = await this.questionPaperModel.countDocuments({
                generatedBy: user._id,
                collegeId: user.collegeId,
                subjectId,
                type: { $ne: 'limit' },
            });
            if (existingPapers >= limitDoc.maxQuestions) {
                throw new common_1.BadRequestException(`You have reached the maximum number of question papers (${limitDoc.maxQuestions}) allowed for this subject`);
            }
        }
    }
    async filterUnusedQuestions(questions, user, subjectId, topicId) {
        if (user.role === 'superAdmin') {
            return questions;
        }
        if (user.collegeId) {
            const availableQuestionIds = questions.map((q) => q._id.toString());
            const unusedQuestionIds = await this.questionUsageService.getUnusedQuestions(user.collegeId, availableQuestionIds, {
                subjectId,
                ...(topicId && { topicId }),
            });
            return questions.filter((q) => unusedQuestionIds.includes(q._id.toString()));
        }
        const usedQuestions = await this.questionPaperModel.distinct('questions', {
            collegeId: user.collegeId,
            subjectId,
            ...(topicId && { topicId }),
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        });
        return questions.filter((q) => !usedQuestions.map((id) => id.toString()).includes(q._id.toString()));
    }
    async selectQuestionsByDifficulty(questions, numberOfQuestions, difficultyMode, customDifficulty) {
        let easyPercentage = 30;
        let mediumPercentage = 50;
        let hardPercentage = 20;
        if (difficultyMode === 'custom' && customDifficulty) {
            easyPercentage = customDifficulty.easyPercentage;
            mediumPercentage = customDifficulty.mediumPercentage;
            hardPercentage = customDifficulty.hardPercentage;
        }
        const easyCount = Math.round((numberOfQuestions * easyPercentage) / 100);
        const mediumCount = Math.round((numberOfQuestions * mediumPercentage) / 100);
        const hardCount = numberOfQuestions - easyCount - mediumCount;
        const easyQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'easy');
        const mediumQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'medium');
        const hardQuestions = questions.filter((q) => (q.difficulty || 'medium') === 'hard');
        const selectedQuestions = [];
        const shuffledEasy = easyQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledEasy.slice(0, Math.min(easyCount, shuffledEasy.length)));
        const shuffledMedium = mediumQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledMedium.slice(0, Math.min(mediumCount, shuffledMedium.length)));
        const shuffledHard = hardQuestions.sort(() => Math.random() - 0.5);
        selectedQuestions.push(...shuffledHard.slice(0, Math.min(hardCount, shuffledHard.length)));
        if (selectedQuestions.length < numberOfQuestions) {
            const remainingQuestions = questions.filter((q) => !selectedQuestions
                .map((sq) => sq._id.toString())
                .includes(q._id.toString()));
            const shuffledRemaining = remainingQuestions.sort(() => Math.random() - 0.5);
            selectedQuestions.push(...shuffledRemaining.slice(0, numberOfQuestions - selectedQuestions.length));
        }
        return selectedQuestions.slice(0, numberOfQuestions);
    }
};
exports.QuestionPapersService = QuestionPapersService;
exports.QuestionPapersService = QuestionPapersService = QuestionPapersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(question_paper_schema_1.QuestionPaper.name)),
    __param(1, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __param(2, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        tracking_service_1.TrackingService,
        question_usage_service_1.QuestionUsageService])
], QuestionPapersService);
//# sourceMappingURL=question-papers.service.js.map