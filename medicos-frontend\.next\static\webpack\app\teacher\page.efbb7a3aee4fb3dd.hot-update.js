"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - no valid token found\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API Error Response:\", errorData);\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});