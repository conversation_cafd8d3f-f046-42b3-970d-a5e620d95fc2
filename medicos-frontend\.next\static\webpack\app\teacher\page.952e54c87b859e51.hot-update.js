"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/debug/TokenDebug.tsx":
/*!*********************************************!*\
  !*** ./src/components/debug/TokenDebug.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenDebug: () => (/* binding */ TokenDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TokenDebug auto */ \nvar _s = $RefreshSig$();\n\nfunction TokenDebug() {\n    _s();\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TokenDebug.useEffect\": ()=>{\n            // Check all possible token keys used in the codebase\n            const tokenKeys = [\n                'backendToken',\n                'firebaseToken',\n                'token',\n                'accessToken',\n                'authToken'\n            ];\n            const tokenData = {};\n            tokenKeys.forEach({\n                \"TokenDebug.useEffect\": (key)=>{\n                    const token = localStorage.getItem(key);\n                    tokenData[key] = token ? \"\".concat(token.substring(0, 30), \"...\") : null;\n                }\n            }[\"TokenDebug.useEffect\"]);\n            setTokens(tokenData);\n        }\n    }[\"TokenDebug.useEffect\"], []);\n    const clearAllTokens = ()=>{\n        Object.keys(tokens).forEach((key)=>{\n            localStorage.removeItem(key);\n        });\n        setTokens({});\n        alert(\"All tokens cleared!\");\n    };\n    const refreshTokens = ()=>{\n        const tokenKeys = [\n            'backendToken',\n            'firebaseToken',\n            'token',\n            'accessToken',\n            'authToken'\n        ];\n        const tokenData = {};\n        tokenKeys.forEach((key)=>{\n            const token = localStorage.getItem(key);\n            tokenData[key] = token ? \"\".concat(token.substring(0, 30), \"...\") : null;\n        });\n        setTokens(tokenData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 border rounded-lg bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-3\",\n                children: \"Token Debug Information\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: Object.entries(tokens).map((param)=>{\n                    let [key, value] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: [\n                                    key,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm \".concat(value ? 'text-green-600' : 'text-red-600'),\n                                children: value || 'Not found'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, key, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: refreshTokens,\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearAllTokens,\n                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\",\n                        children: \"Clear All Tokens\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/login',\n                        className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n                        children: \"Go to Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-sm text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 12\n                            }, this),\n                            \" If no tokens are found, you may need to log in again.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The API requires a valid authentication token to create question papers.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\debug\\\\TokenDebug.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenDebug, \"V06netxq9aW13FKh/oYxaoV9/uo=\");\n_c = TokenDebug;\nvar _c;\n$RefreshReg$(_c, \"TokenDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/debug/TokenDebug.tsx\n"));

/***/ })

});