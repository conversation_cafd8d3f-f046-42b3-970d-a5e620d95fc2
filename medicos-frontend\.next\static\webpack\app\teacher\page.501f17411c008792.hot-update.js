"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please enter a title for the question paper\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.questionType) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select an exam type\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.subject) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select a subject\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            const questionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"Question paper created:\", questionPaper);\n            // Show creation success\n            toast({\n                title: \"Question Paper Created\",\n                description: \"Starting PDF download...\",\n                variant: \"default\"\n            });\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Success!\",\n                description: \"Question paper generated and downloaded successfully!\",\n                variant: \"default\"\n            });\n            // Reset to first step and clear form data after a short delay\n            setTimeout(()=>{\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n            }, 1000) // 1 second delay to ensure toast is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            console.error(\"Error submitting form:\", error);\n            let errorMessage = \"An unknown error occurred\";\n            let errorTitle = \"Error\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === 'string') {\n                errorMessage = error;\n            }\n            // Handle specific error types with better titles and messages\n            if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                errorTitle = \"Authentication Error\";\n                errorMessage = \"Please log in again to continue. Your session may have expired.\";\n            } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                errorTitle = \"Network Error\";\n                errorMessage = \"Please check your internet connection and try again.\";\n            } else if (errorMessage.includes(\"unused questions available\")) {\n                errorTitle = \"Insufficient Questions\";\n            // Keep the original message as it's already clear\n            } else if (errorMessage.includes(\"questions available\")) {\n                errorTitle = \"Question Availability Issue\";\n            // Keep the original message\n            }\n            // Always show toast with proper error handling\n            setTimeout(()=>{\n                toast({\n                    title: errorTitle,\n                    description: errorMessage,\n                    variant: \"destructive\",\n                    duration: 5000\n                });\n            }, 100); // Small delay to ensure toast system is ready\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_10__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 351,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 356,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"afGr6afeaaVmX0zn7abb7TEJzGk=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});