"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                alert(\"Please select an exam type\");\n                return;\n            }\n            if (!formData.subject) {\n                alert(\"Please select a subject\");\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Creating question paper with payload:\", apiPayload);\n            const questionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"Question paper created successfully:\", questionPaper);\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            // Reset to first step and clear form data after a short delay\n            setTimeout(()=>{\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n            }, 1000) // 1 second delay to ensure toast is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            console.error(\"Error submitting form:\", error);\n            let errorMessage = \"An unknown error occurred\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === 'string') {\n                errorMessage = error;\n            }\n            // Handle specific error types with better messages\n            if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                errorMessage = \"Please log in again to continue. Your session may have expired.\";\n            } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                errorMessage = \"Please check your internet connection and try again.\";\n            } else if (errorMessage.includes(\"unused questions available\")) {\n                // Extract numbers from the error message for a clearer explanation\n                const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                if (match) {\n                    const available = match[1];\n                    const requested = match[2];\n                    errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                }\n            }\n            // Show error message in alert\n            alert(\"Error: \".concat(errorMessage));\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 319,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});