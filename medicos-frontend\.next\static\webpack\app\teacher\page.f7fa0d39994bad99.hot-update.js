"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            if (!formData.subject) {\n                setIsGenerating(false);\n                alert(\"Please select a subject\");\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            // Check if the request was successful\n            if (!result.success) {\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            const questionPaper = result.data;\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 329,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});