"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        console.log(\"API URL:\", \"\".concat(API_BASE_URL, \"/question-papers\"));\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            let errorMessage = \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            try {\n                // Try to get error message from response body\n                const errorText = await response.text();\n                console.log(\"API Error Response Text:\", errorText);\n                if (errorText) {\n                    try {\n                        // Try to parse as JSON first\n                        const errorData = JSON.parse(errorText);\n                        console.log(\"API Error Response JSON:\", errorData);\n                        // Extract the message from the parsed JSON\n                        if (errorData && errorData.message) {\n                            errorMessage = errorData.message;\n                        } else if (errorData && errorData.error) {\n                            errorMessage = errorData.error;\n                        } else {\n                            errorMessage = errorText;\n                        }\n                    } catch (jsonError) {\n                        // If not JSON, use the text directly\n                        console.log(\"Failed to parse as JSON, using raw text:\", jsonError);\n                        errorMessage = errorText;\n                    }\n                }\n            } catch (parseError) {\n                console.error(\"Failed to parse error response:\", parseError);\n            }\n            // Provide more specific error messages based on status code if we don't have a message\n            if (!errorMessage || errorMessage === \"Error: \".concat(response.status, \" - \").concat(response.statusText)) {\n                switch(response.status){\n                    case 401:\n                        errorMessage = \"Authentication required - Please log in again.\";\n                        break;\n                    case 403:\n                        errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                        break;\n                    case 404:\n                        errorMessage = \"Resource not found - The requested item could not be found.\";\n                        break;\n                    case 429:\n                        errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                        break;\n                    case 500:\n                        errorMessage = \"Server error - Please try again later.\";\n                        break;\n                    case 503:\n                        errorMessage = \"Service unavailable - The server is temporarily down.\";\n                        break;\n                    default:\n                        if (response.status >= 400 && response.status < 500) {\n                            errorMessage = \"Invalid request - Please check your input and try again.\";\n                        } else if (response.status >= 500) {\n                            errorMessage = \"Server error - Please try again later.\";\n                        }\n                }\n            }\n            console.log(\"Final error message being thrown:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});