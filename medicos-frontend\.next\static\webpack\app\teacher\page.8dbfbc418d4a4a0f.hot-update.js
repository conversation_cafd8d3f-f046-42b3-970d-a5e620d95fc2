"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API Error Response:\", errorData);\n            // Provide more specific error messages based on status code\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            switch(response.status){\n                case 401:\n                    errorMessage = \"Authentication required - Please log in again.\";\n                    break;\n                case 403:\n                    errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                    break;\n                case 404:\n                    errorMessage = \"Resource not found - The requested item could not be found.\";\n                    break;\n                case 429:\n                    errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                    break;\n                case 500:\n                    errorMessage = \"Server error - Please try again later.\";\n                    break;\n                case 503:\n                    errorMessage = \"Service unavailable - The server is temporarily down.\";\n                    break;\n                default:\n                    if (response.status >= 400 && response.status < 500) {\n                        errorMessage = errorData.message || \"Invalid request - Please check your input and try again.\";\n                    } else if (response.status >= 500) {\n                        errorMessage = \"Server error - Please try again later.\";\n                    }\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});