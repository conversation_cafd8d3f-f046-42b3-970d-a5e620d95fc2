"use client"

import { useState } from "react"
import { createQuestionPaper, downloadQuestionPaper, CreateQuestionPaperDto } from "@/lib/api/questionPapers"
import { QuestionTypeStep } from "./steps/question-type-step"
import { CourseSubjectStep } from "./steps/course-subject-step"
import { DifficultyLevelStep } from "./steps/difficulty-level-step"
import { QuestionSelectionStep } from "./steps/question-selection-step"
import { PaperCustomizationStep } from "./steps/paper-customization-step"
import { IncludeAnswersStep } from "./steps/include-answers-step"
import { ActionsStep } from "./steps/actions-step"
import { StepIndicator } from "./ui/step-indicator"
import { QuestionTitleAndDescriptionStep } from "./steps/question-title-description-step"

export type FormData = {
  // QuestionTypeStep - maps to examType
  questionType: string
  // QuestionTitleAndDescriptionStep - maps to title, description
  title: string
  description: string
  // CourseSubjectStep - maps to subject
  course: string
  subject: string
  // DifficultyLevelStep - maps to customise.customDifficulty or auto
  difficultyMode: "auto" | "custom"
  difficultyLevels: {
    easy: number
    medium: number
    hard: number
  }
  // QuestionSelectionStep - maps to maxQuestions and customise.numberOfQuestions
  numberOfQuestions: number
  // PaperCustomizationStep - maps to totalMarks and customise.totalMarks
  totalMarks: number
  // IncludeAnswersStep - maps to customise.includeAnswers
  includeAnswers: boolean
  // Additional fields for API
  duration: number // Default 60 minutes (1 hour)
  instructions: string
  topicId?: string
}

const initialFormData: FormData = {
  questionType: "",
  title: "",
  description: "",
  course: "",
  subject: "",
  difficultyMode: "auto",
  difficultyLevels: {
    easy: 30,
    medium: 50,
    hard: 20,
  },
  numberOfQuestions: 1,
  totalMarks: 100,
  includeAnswers: false, // Default to false as per requirements
  duration: 60, // Default 1 hour in minutes
  instructions: "",
  topicId: undefined,
}

export function QuestionPaperWizard() {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<FormData>(initialFormData)
  const [isGenerating, setIsGenerating] = useState(false)

  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({ ...prev, ...data }))
  }

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1))
  }

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0))
  }

  const skipStep = () => {
    nextStep()
  }

  const handleSubmit = async () => {
    if (isGenerating) return; // Prevent multiple submissions

    try {
      setIsGenerating(true);
      console.log("Submitting data:", formData)

      // Debug: Check available tokens
      const backendToken = localStorage.getItem("backendToken");
      const firebaseToken = localStorage.getItem("firebaseToken");
      const token = localStorage.getItem("token");

      console.log("Available tokens:", {
        backendToken: backendToken ? `${backendToken.substring(0, 20)}...` : 'None',
        firebaseToken: firebaseToken ? `${firebaseToken.substring(0, 20)}...` : 'None',
        token: token ? `${token.substring(0, 20)}...` : 'None'
      });

      // Validate required fields
      if (!formData.title?.trim()) {
        setIsGenerating(false);
        alert("Please enter a title for the question paper");
        return;
      }

      if (!formData.questionType) {
        setIsGenerating(false);
        alert("Please select an exam type");
        return;
      }

      if (!formData.subject) {
        setIsGenerating(false);
        alert("Please select a subject");
        return;
      }

      // Prepare the API payload
      const apiPayload: CreateQuestionPaperDto = {
        title: formData.title,
        description: formData.description,
        subject: formData.subject,
        totalMarks: formData.totalMarks,
        duration: formData.duration,
        examType: formData.questionType,
        instructions: formData.instructions,
        topicId: formData.topicId,
        maxQuestions: formData.numberOfQuestions,
      }

      // Add customization if not auto mode
      if (formData.difficultyMode === "custom") {
        apiPayload.customise = {
          customDifficulty: {
            easyPercentage: formData.difficultyLevels.easy,
            mediumPercentage: formData.difficultyLevels.medium,
            hardPercentage: formData.difficultyLevels.hard,
          },
          numberOfQuestions: formData.numberOfQuestions,
          totalMarks: formData.totalMarks,
          duration: formData.duration,
          includeAnswers: formData.includeAnswers,
        }
      } else {
        // For auto mode, still include customization with default values
        apiPayload.customise = {
          customDifficulty: {
            easyPercentage: 30,
            mediumPercentage: 50,
            hardPercentage: 20,
          },
          numberOfQuestions: formData.numberOfQuestions,
          totalMarks: formData.totalMarks,
          duration: formData.duration,
          includeAnswers: formData.includeAnswers,
        }
      }

      // Create the question paper
      console.log("Creating question paper with payload:", apiPayload);
      const questionPaper = await createQuestionPaper(apiPayload)
      console.log("Question paper created successfully:", questionPaper)

      // Download the PDF
      const pdfBlob = await downloadQuestionPaper(questionPaper._id, 'pdf')

      // Create download link
      const url = window.URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${formData.title.replace(/\s+/g, '_')}_${Date.now()}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      alert("Question paper generated and downloaded successfully!");

      // Reset to first step and clear form data after a short delay (only on success)
      setTimeout(() => {
        setCurrentStep(0)
        setFormData(initialFormData)
        setIsGenerating(false)
      }, 1000) // 1 second delay to ensure alert is visible
    } catch (error) {
      setIsGenerating(false)

      // Suppress the default error logging to avoid console clutter
      let errorMessage = "An unknown error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Handle specific error types with better messages
      if (errorMessage.includes("Authentication required") || errorMessage.includes("Unauthorized")) {
        errorMessage = "Please log in again to continue. Your session may have expired.";
      } else if (errorMessage.includes("Network") || errorMessage.includes("fetch")) {
        errorMessage = "Please check your internet connection and try again.";
      } else if (errorMessage.includes("unused questions available")) {
        // Extract numbers from the error message for a clearer explanation
        const match = errorMessage.match(/Only (\d+) unused questions available\. Requested: (\d+)/);
        if (match) {
          const available = match[1];
          const requested = match[2];
          errorMessage = `Only ${available} questions are available for this subject/topic, but you requested ${requested} questions. Please reduce the number of questions or add more questions to the database.`;
        }
      }

      // Show error message in alert
      alert(`Error: ${errorMessage}`);

      // DO NOT reset form or step on error - user should stay where they are
    }
  }

  const steps = [
    {
      title: "Question Type",
      icon: "HelpCircle",
      component: (
        <QuestionTypeStep 
          formData={formData} 
          updateFormData={updateFormData} 
          onNext={nextStep} 
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
        {
      title: "Question Type",
      icon: "HelpCircle",
      component: (
        <QuestionTitleAndDescriptionStep 
          formData={formData} 
          updateFormData={updateFormData} 
          onNext={nextStep} 
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Course & Subject Selection",
      icon: "BookOpen",
      component: (
        <CourseSubjectStep 
          formData={formData} 
          updateFormData={updateFormData} 
          onNext={nextStep} 
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Select Difficulty Level",
      icon: "BarChart2",
      component: (
        <DifficultyLevelStep 
          formData={formData} 
          updateFormData={updateFormData} 
          onNext={nextStep} 
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Question Selection Criteria",
      icon: "FileText",
      component: (
        <QuestionSelectionStep
          formData={formData}
          updateFormData={updateFormData}
          onNext={nextStep}
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Paper Customization",
      icon: "FileEdit",
      component: (
        <PaperCustomizationStep
          formData={formData}
          updateFormData={updateFormData}
          onNext={nextStep}
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Include Answers?",
      icon: "CheckSquare",
      component: (
        <IncludeAnswersStep 
          formData={formData} 
          updateFormData={updateFormData} 
          onNext={nextStep} 
          onSkip={skipStep}
          onBack={prevStep}
          backDisabled={currentStep === 0}
        />
      ),
    },
    {
      title: "Actions",
      icon: "FileOutput",
      component: <ActionsStep formData={formData} onSubmit={handleSubmit} isLoading={isGenerating} />,
    },
  ]



  return (
    <div className="space-y-6">
      <StepIndicator currentStep={currentStep} steps={steps.map((step) => ({ title: step.title, icon: step.icon }))} />
      {steps[currentStep].component}
    </div>
  )
}
