"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        console.log(\"Creating question paper with data:\", questionPaperData);\n        console.log(\"Using headers:\", {\n            ...headers,\n            Authorization: headers.Authorization ? \"Bearer \".concat(headers.Authorization.substring(0, 20), \"...\") : 'None'\n        });\n        console.log(\"API URL:\", \"\".concat(API_BASE_URL, \"/question-papers\"));\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            let errorMessage = \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            try {\n                // Try to get error message from response body\n                const errorText = await response.text();\n                console.error(\"API Error Response Text:\", errorText);\n                if (errorText) {\n                    try {\n                        // Try to parse as JSON first\n                        const errorData = JSON.parse(errorText);\n                        console.error(\"API Error Response JSON:\", errorData);\n                        errorMessage = errorData.message || errorData.error || errorText;\n                    } catch (e) {\n                        // If not JSON, use the text directly\n                        errorMessage = errorText;\n                    }\n                }\n            } catch (parseError) {\n                console.error(\"Failed to parse error response:\", parseError);\n            }\n            // Provide more specific error messages based on status code if we don't have a message\n            if (!errorMessage || errorMessage === \"Error: \".concat(response.status, \" - \").concat(response.statusText)) {\n                switch(response.status){\n                    case 401:\n                        errorMessage = \"Authentication required - Please log in again.\";\n                        break;\n                    case 403:\n                        errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                        break;\n                    case 404:\n                        errorMessage = \"Resource not found - The requested item could not be found.\";\n                        break;\n                    case 429:\n                        errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                        break;\n                    case 500:\n                        errorMessage = \"Server error - Please try again later.\";\n                        break;\n                    case 503:\n                        errorMessage = \"Service unavailable - The server is temporarily down.\";\n                        break;\n                    default:\n                        if (response.status >= 400 && response.status < 500) {\n                            errorMessage = \"Invalid request - Please check your input and try again.\";\n                        } else if (response.status >= 500) {\n                            errorMessage = \"Server error - Please try again later.\";\n                        }\n                }\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});