"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please enter a title for the question paper\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.questionType) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select an exam type\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.subject) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select a subject\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            const questionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"Question paper created:\", questionPaper);\n            // Show creation success\n            toast({\n                title: \"Question Paper Created\",\n                description: \"Starting PDF download...\",\n                variant: \"default\"\n            });\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Success!\",\n                description: \"Question paper generated and downloaded successfully!\",\n                variant: \"default\"\n            });\n            // Reset to first step and clear form data after a short delay\n            setTimeout(()=>{\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n            }, 1000) // 1 second delay to ensure toast is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            console.error(\"Error submitting form:\", error);\n            let errorMessage = \"An unknown error occurred\";\n            let errorTitle = \"Error\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === 'string') {\n                errorMessage = error;\n            }\n            // Handle specific error types with better titles and messages\n            if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                errorTitle = \"Authentication Error\";\n                errorMessage = \"Please log in again to continue. Your session may have expired.\";\n            } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                errorTitle = \"Network Error\";\n                errorMessage = \"Please check your internet connection and try again.\";\n            } else if (errorMessage.includes(\"unused questions available\")) {\n                errorTitle = \"Insufficient Questions\";\n                // Extract numbers from the error message for a clearer explanation\n                const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                if (match) {\n                    const available = match[1];\n                    const requested = match[2];\n                    errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                }\n            } else if (errorMessage.includes(\"questions available\")) {\n                errorTitle = \"Question Availability Issue\";\n            // Keep the original message\n            }\n            // Always show toast with proper error handling\n            console.log(\"Showing error toast:\", {\n                errorTitle,\n                errorMessage\n            });\n            // Force the toast to show by using both methods\n            setTimeout(()=>{\n                console.log(\"Attempting to show toast now...\");\n                // Try the hook-based toast\n                toast({\n                    title: errorTitle,\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n                // Also try the standalone toast as backup\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    title: errorTitle,\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }, 500); // Longer delay to ensure any loading states are cleared\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 356,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_10__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 369,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    // Test toast function\n    const testToast = ()=>{\n        console.log(\"Test toast clicked\");\n        toast({\n            title: \"Test Toast (Hook)\",\n            description: \"This is a test toast using the hook method\",\n            variant: \"destructive\"\n        });\n        // Also test standalone\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n            title: \"Test Toast (Standalone)\",\n            description: \"This is a test toast using the standalone method\",\n            variant: \"destructive\"\n        });\n    };\n    // Test error toast function\n    const testErrorToast = ()=>{\n        console.log(\"Test error toast clicked\");\n        const errorTitle = \"Insufficient Questions\";\n        const errorMessage = \"Only 2 questions are available for this subject/topic, but you requested 3 questions. Please reduce the number of questions or add more questions to the database.\";\n        setTimeout(()=>{\n            console.log(\"Showing test error toast...\");\n            toast({\n                title: errorTitle,\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: errorTitle,\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n        }, 500);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: testToast,\n                className: \"px-4 py-2 bg-red-500 text-white rounded\",\n                style: {\n                    position: 'fixed',\n                    top: '10px',\n                    right: '10px',\n                    zIndex: 9999\n                },\n                children: \"Test Toast\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: testErrorToast,\n                className: \"px-4 py-2 bg-orange-500 text-white rounded\",\n                style: {\n                    position: 'fixed',\n                    top: '60px',\n                    right: '10px',\n                    zIndex: 9999\n                },\n                children: \"Test Error Toast\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"afGr6afeaaVmX0zn7abb7TEJzGk=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});