"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please enter a title for the question paper\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.questionType) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select an exam type\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            if (!formData.subject) {\n                toast({\n                    title: \"Validation Error\",\n                    description: \"Please select a subject\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            const questionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"Question paper created:\", questionPaper);\n            // Show creation success\n            toast({\n                title: \"Question Paper Created\",\n                description: \"Starting PDF download...\",\n                variant: \"default\"\n            });\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Success!\",\n                description: \"Question paper generated and downloaded successfully!\",\n                variant: \"default\"\n            });\n            // Reset to first step and clear form data after a short delay\n            setTimeout(()=>{\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n            }, 1000) // 1 second delay to ensure toast is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            console.error(\"Error submitting form:\", error);\n            let errorMessage = \"An unknown error occurred\";\n            if (error instanceof Error) {\n                errorMessage = error.message;\n            } else if (typeof error === 'string') {\n                errorMessage = error;\n            }\n            // Handle specific error types\n            if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                toast({\n                    title: \"Authentication Error\",\n                    description: \"Please log in again to continue. Your session may have expired.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                toast({\n                    title: \"Network Error\",\n                    description: \"Please check your internet connection and try again.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_4__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_12__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_5__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_6__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_7__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_8__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_9__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_10__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 346,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_11__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"afGr6afeaaVmX0zn7abb7TEJzGk=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});